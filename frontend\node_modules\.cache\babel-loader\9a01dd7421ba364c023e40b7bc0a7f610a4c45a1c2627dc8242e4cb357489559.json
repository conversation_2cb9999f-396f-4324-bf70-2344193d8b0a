{"ast": null, "code": "'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"elevation\", \"square\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport getOverlayAlpha from '../styles/getOverlayAlpha';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport { getPaperUtilityClass } from './paperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$vars$overlays;\n  return _extends({\n    backgroundColor: (theme.vars || theme).palette.background.paper,\n    color: (theme.vars || theme).palette.text.primary,\n    transition: theme.transitions.create('box-shadow')\n  }, !ownerState.square && {\n    borderRadius: theme.shape.borderRadius\n  }, ownerState.variant === 'outlined' && {\n    border: `1px solid ${(theme.vars || theme).palette.divider}`\n  }, ownerState.variant === 'elevation' && _extends({\n    boxShadow: (theme.vars || theme).shadows[ownerState.elevation]\n  }, !theme.vars && theme.palette.mode === 'dark' && {\n    backgroundImage: `linear-gradient(${alpha('#fff', getOverlayAlpha(ownerState.elevation))}, ${alpha('#fff', getOverlayAlpha(ownerState.elevation))})`\n  }, theme.vars && {\n    backgroundImage: (_theme$vars$overlays = theme.vars.overlays) == null ? void 0 : _theme$vars$overlays[ownerState.elevation]\n  }));\n});\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const {\n      className,\n      component = 'div',\n      elevation = 1,\n      square = false,\n      variant = 'elevation'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    elevation,\n    square,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const theme = useTheme();\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "React", "PropTypes", "clsx", "integerPropType", "chainPropTypes", "composeClasses", "alpha", "styled", "getOverlayAlpha", "useDefaultProps", "useTheme", "getPaperUtilityClass", "jsx", "_jsx", "useUtilityClasses", "ownerState", "square", "elevation", "variant", "classes", "slots", "root", "PaperRoot", "name", "slot", "overridesResolver", "props", "styles", "rounded", "theme", "_theme$vars$overlays", "backgroundColor", "vars", "palette", "background", "paper", "color", "text", "primary", "transition", "transitions", "create", "borderRadius", "shape", "border", "divider", "boxShadow", "shadows", "mode", "backgroundImage", "overlays", "Paper", "forwardRef", "inProps", "ref", "className", "component", "other", "process", "env", "NODE_ENV", "undefined", "console", "error", "join", "as", "propTypes", "children", "node", "object", "string", "elementType", "Error", "bool", "sx", "oneOfType", "arrayOf", "func", "oneOf"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/material/Paper/Paper.js"], "sourcesContent": ["'use client';\n\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"className\", \"component\", \"elevation\", \"square\", \"variant\"];\nimport * as React from 'react';\nimport PropTypes from 'prop-types';\nimport clsx from 'clsx';\nimport integerPropType from '@mui/utils/integerPropType';\nimport chainPropTypes from '@mui/utils/chainPropTypes';\nimport composeClasses from '@mui/utils/composeClasses';\nimport { alpha } from '@mui/system/colorManipulator';\nimport styled from '../styles/styled';\nimport getOverlayAlpha from '../styles/getOverlayAlpha';\nimport { useDefaultProps } from '../DefaultPropsProvider';\nimport useTheme from '../styles/useTheme';\nimport { getPaperUtilityClass } from './paperClasses';\nimport { jsx as _jsx } from \"react/jsx-runtime\";\nconst useUtilityClasses = ownerState => {\n  const {\n    square,\n    elevation,\n    variant,\n    classes\n  } = ownerState;\n  const slots = {\n    root: ['root', variant, !square && 'rounded', variant === 'elevation' && `elevation${elevation}`]\n  };\n  return composeClasses(slots, getPaperUtilityClass, classes);\n};\nconst PaperRoot = styled('div', {\n  name: 'MuiPaper',\n  slot: 'Root',\n  overridesResolver: (props, styles) => {\n    const {\n      ownerState\n    } = props;\n    return [styles.root, styles[ownerState.variant], !ownerState.square && styles.rounded, ownerState.variant === 'elevation' && styles[`elevation${ownerState.elevation}`]];\n  }\n})(({\n  theme,\n  ownerState\n}) => {\n  var _theme$vars$overlays;\n  return _extends({\n    backgroundColor: (theme.vars || theme).palette.background.paper,\n    color: (theme.vars || theme).palette.text.primary,\n    transition: theme.transitions.create('box-shadow')\n  }, !ownerState.square && {\n    borderRadius: theme.shape.borderRadius\n  }, ownerState.variant === 'outlined' && {\n    border: `1px solid ${(theme.vars || theme).palette.divider}`\n  }, ownerState.variant === 'elevation' && _extends({\n    boxShadow: (theme.vars || theme).shadows[ownerState.elevation]\n  }, !theme.vars && theme.palette.mode === 'dark' && {\n    backgroundImage: `linear-gradient(${alpha('#fff', getOverlayAlpha(ownerState.elevation))}, ${alpha('#fff', getOverlayAlpha(ownerState.elevation))})`\n  }, theme.vars && {\n    backgroundImage: (_theme$vars$overlays = theme.vars.overlays) == null ? void 0 : _theme$vars$overlays[ownerState.elevation]\n  }));\n});\nconst Paper = /*#__PURE__*/React.forwardRef(function Paper(inProps, ref) {\n  const props = useDefaultProps({\n    props: inProps,\n    name: 'MuiPaper'\n  });\n  const {\n      className,\n      component = 'div',\n      elevation = 1,\n      square = false,\n      variant = 'elevation'\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const ownerState = _extends({}, props, {\n    component,\n    elevation,\n    square,\n    variant\n  });\n  const classes = useUtilityClasses(ownerState);\n  if (process.env.NODE_ENV !== 'production') {\n    // eslint-disable-next-line react-hooks/rules-of-hooks\n    const theme = useTheme();\n    if (theme.shadows[elevation] === undefined) {\n      console.error([`MUI: The elevation provided <Paper elevation={${elevation}}> is not available in the theme.`, `Please make sure that \\`theme.shadows[${elevation}]\\` is defined.`].join('\\n'));\n    }\n  }\n  return /*#__PURE__*/_jsx(PaperRoot, _extends({\n    as: component,\n    ownerState: ownerState,\n    className: clsx(classes.root, className),\n    ref: ref\n  }, other));\n});\nprocess.env.NODE_ENV !== \"production\" ? Paper.propTypes /* remove-proptypes */ = {\n  // ┌────────────────────────────── Warning ──────────────────────────────┐\n  // │ These PropTypes are generated from the TypeScript type definitions. │\n  // │    To update them, edit the d.ts file and run `pnpm proptypes`.     │\n  // └─────────────────────────────────────────────────────────────────────┘\n  /**\n   * The content of the component.\n   */\n  children: PropTypes.node,\n  /**\n   * Override or extend the styles applied to the component.\n   */\n  classes: PropTypes.object,\n  /**\n   * @ignore\n   */\n  className: PropTypes.string,\n  /**\n   * The component used for the root node.\n   * Either a string to use a HTML element or a component.\n   */\n  component: PropTypes.elementType,\n  /**\n   * Shadow depth, corresponds to `dp` in the spec.\n   * It accepts values between 0 and 24 inclusive.\n   * @default 1\n   */\n  elevation: chainPropTypes(integerPropType, props => {\n    const {\n      elevation,\n      variant\n    } = props;\n    if (elevation > 0 && variant === 'outlined') {\n      return new Error(`MUI: Combining \\`elevation={${elevation}}\\` with \\`variant=\"${variant}\"\\` has no effect. Either use \\`elevation={0}\\` or use a different \\`variant\\`.`);\n    }\n    return null;\n  }),\n  /**\n   * If `true`, rounded corners are disabled.\n   * @default false\n   */\n  square: PropTypes.bool,\n  /**\n   * The system prop that allows defining system overrides as well as additional CSS styles.\n   */\n  sx: PropTypes.oneOfType([PropTypes.arrayOf(PropTypes.oneOfType([PropTypes.func, PropTypes.object, PropTypes.bool])), PropTypes.func, PropTypes.object]),\n  /**\n   * The variant to use.\n   * @default 'elevation'\n   */\n  variant: PropTypes /* @typescript-to-proptypes-ignore */.oneOfType([PropTypes.oneOf(['elevation', 'outlined']), PropTypes.string])\n} : void 0;\nexport default Paper;"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,WAAW,EAAE,WAAW,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,CAAC;AAC9E,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,IAAI,MAAM,MAAM;AACvB,OAAOC,eAAe,MAAM,4BAA4B;AACxD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,OAAOC,cAAc,MAAM,2BAA2B;AACtD,SAASC,KAAK,QAAQ,8BAA8B;AACpD,OAAOC,MAAM,MAAM,kBAAkB;AACrC,OAAOC,eAAe,MAAM,2BAA2B;AACvD,SAASC,eAAe,QAAQ,yBAAyB;AACzD,OAAOC,QAAQ,MAAM,oBAAoB;AACzC,SAASC,oBAAoB,QAAQ,gBAAgB;AACrD,SAASC,GAAG,IAAIC,IAAI,QAAQ,mBAAmB;AAC/C,MAAMC,iBAAiB,GAAGC,UAAU,IAAI;EACtC,MAAM;IACJC,MAAM;IACNC,SAAS;IACTC,OAAO;IACPC;EACF,CAAC,GAAGJ,UAAU;EACd,MAAMK,KAAK,GAAG;IACZC,IAAI,EAAE,CAAC,MAAM,EAAEH,OAAO,EAAE,CAACF,MAAM,IAAI,SAAS,EAAEE,OAAO,KAAK,WAAW,IAAI,YAAYD,SAAS,EAAE;EAClG,CAAC;EACD,OAAOZ,cAAc,CAACe,KAAK,EAAET,oBAAoB,EAAEQ,OAAO,CAAC;AAC7D,CAAC;AACD,MAAMG,SAAS,GAAGf,MAAM,CAAC,KAAK,EAAE;EAC9BgB,IAAI,EAAE,UAAU;EAChBC,IAAI,EAAE,MAAM;EACZC,iBAAiB,EAAEA,CAACC,KAAK,EAAEC,MAAM,KAAK;IACpC,MAAM;MACJZ;IACF,CAAC,GAAGW,KAAK;IACT,OAAO,CAACC,MAAM,CAACN,IAAI,EAAEM,MAAM,CAACZ,UAAU,CAACG,OAAO,CAAC,EAAE,CAACH,UAAU,CAACC,MAAM,IAAIW,MAAM,CAACC,OAAO,EAAEb,UAAU,CAACG,OAAO,KAAK,WAAW,IAAIS,MAAM,CAAC,YAAYZ,UAAU,CAACE,SAAS,EAAE,CAAC,CAAC;EAC1K;AACF,CAAC,CAAC,CAAC,CAAC;EACFY,KAAK;EACLd;AACF,CAAC,KAAK;EACJ,IAAIe,oBAAoB;EACxB,OAAOhC,QAAQ,CAAC;IACdiC,eAAe,EAAE,CAACF,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACC,UAAU,CAACC,KAAK;IAC/DC,KAAK,EAAE,CAACP,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACI,IAAI,CAACC,OAAO;IACjDC,UAAU,EAAEV,KAAK,CAACW,WAAW,CAACC,MAAM,CAAC,YAAY;EACnD,CAAC,EAAE,CAAC1B,UAAU,CAACC,MAAM,IAAI;IACvB0B,YAAY,EAAEb,KAAK,CAACc,KAAK,CAACD;EAC5B,CAAC,EAAE3B,UAAU,CAACG,OAAO,KAAK,UAAU,IAAI;IACtC0B,MAAM,EAAE,aAAa,CAACf,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEI,OAAO,CAACY,OAAO;EAC5D,CAAC,EAAE9B,UAAU,CAACG,OAAO,KAAK,WAAW,IAAIpB,QAAQ,CAAC;IAChDgD,SAAS,EAAE,CAACjB,KAAK,CAACG,IAAI,IAAIH,KAAK,EAAEkB,OAAO,CAAChC,UAAU,CAACE,SAAS;EAC/D,CAAC,EAAE,CAACY,KAAK,CAACG,IAAI,IAAIH,KAAK,CAACI,OAAO,CAACe,IAAI,KAAK,MAAM,IAAI;IACjDC,eAAe,EAAE,mBAAmB3C,KAAK,CAAC,MAAM,EAAEE,eAAe,CAACO,UAAU,CAACE,SAAS,CAAC,CAAC,KAAKX,KAAK,CAAC,MAAM,EAAEE,eAAe,CAACO,UAAU,CAACE,SAAS,CAAC,CAAC;EACnJ,CAAC,EAAEY,KAAK,CAACG,IAAI,IAAI;IACfiB,eAAe,EAAE,CAACnB,oBAAoB,GAAGD,KAAK,CAACG,IAAI,CAACkB,QAAQ,KAAK,IAAI,GAAG,KAAK,CAAC,GAAGpB,oBAAoB,CAACf,UAAU,CAACE,SAAS;EAC5H,CAAC,CAAC,CAAC;AACL,CAAC,CAAC;AACF,MAAMkC,KAAK,GAAG,aAAanD,KAAK,CAACoD,UAAU,CAAC,SAASD,KAAKA,CAACE,OAAO,EAAEC,GAAG,EAAE;EACvE,MAAM5B,KAAK,GAAGjB,eAAe,CAAC;IAC5BiB,KAAK,EAAE2B,OAAO;IACd9B,IAAI,EAAE;EACR,CAAC,CAAC;EACF,MAAM;MACFgC,SAAS;MACTC,SAAS,GAAG,KAAK;MACjBvC,SAAS,GAAG,CAAC;MACbD,MAAM,GAAG,KAAK;MACdE,OAAO,GAAG;IACZ,CAAC,GAAGQ,KAAK;IACT+B,KAAK,GAAG5D,6BAA6B,CAAC6B,KAAK,EAAE3B,SAAS,CAAC;EACzD,MAAMgB,UAAU,GAAGjB,QAAQ,CAAC,CAAC,CAAC,EAAE4B,KAAK,EAAE;IACrC8B,SAAS;IACTvC,SAAS;IACTD,MAAM;IACNE;EACF,CAAC,CAAC;EACF,MAAMC,OAAO,GAAGL,iBAAiB,CAACC,UAAU,CAAC;EAC7C,IAAI2C,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;IACzC;IACA,MAAM/B,KAAK,GAAGnB,QAAQ,CAAC,CAAC;IACxB,IAAImB,KAAK,CAACkB,OAAO,CAAC9B,SAAS,CAAC,KAAK4C,SAAS,EAAE;MAC1CC,OAAO,CAACC,KAAK,CAAC,CAAC,iDAAiD9C,SAAS,mCAAmC,EAAE,yCAAyCA,SAAS,iBAAiB,CAAC,CAAC+C,IAAI,CAAC,IAAI,CAAC,CAAC;IAChM;EACF;EACA,OAAO,aAAanD,IAAI,CAACS,SAAS,EAAExB,QAAQ,CAAC;IAC3CmE,EAAE,EAAET,SAAS;IACbzC,UAAU,EAAEA,UAAU;IACtBwC,SAAS,EAAErD,IAAI,CAACiB,OAAO,CAACE,IAAI,EAAEkC,SAAS,CAAC;IACxCD,GAAG,EAAEA;EACP,CAAC,EAAEG,KAAK,CAAC,CAAC;AACZ,CAAC,CAAC;AACFC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAGT,KAAK,CAACe,SAAS,CAAC,yBAAyB;EAC/E;EACA;EACA;EACA;EACA;AACF;AACA;EACEC,QAAQ,EAAElE,SAAS,CAACmE,IAAI;EACxB;AACF;AACA;EACEjD,OAAO,EAAElB,SAAS,CAACoE,MAAM;EACzB;AACF;AACA;EACEd,SAAS,EAAEtD,SAAS,CAACqE,MAAM;EAC3B;AACF;AACA;AACA;EACEd,SAAS,EAAEvD,SAAS,CAACsE,WAAW;EAChC;AACF;AACA;AACA;AACA;EACEtD,SAAS,EAAEb,cAAc,CAACD,eAAe,EAAEuB,KAAK,IAAI;IAClD,MAAM;MACJT,SAAS;MACTC;IACF,CAAC,GAAGQ,KAAK;IACT,IAAIT,SAAS,GAAG,CAAC,IAAIC,OAAO,KAAK,UAAU,EAAE;MAC3C,OAAO,IAAIsD,KAAK,CAAC,+BAA+BvD,SAAS,uBAAuBC,OAAO,iFAAiF,CAAC;IAC3K;IACA,OAAO,IAAI;EACb,CAAC,CAAC;EACF;AACF;AACA;AACA;EACEF,MAAM,EAAEf,SAAS,CAACwE,IAAI;EACtB;AACF;AACA;EACEC,EAAE,EAAEzE,SAAS,CAAC0E,SAAS,CAAC,CAAC1E,SAAS,CAAC2E,OAAO,CAAC3E,SAAS,CAAC0E,SAAS,CAAC,CAAC1E,SAAS,CAAC4E,IAAI,EAAE5E,SAAS,CAACoE,MAAM,EAAEpE,SAAS,CAACwE,IAAI,CAAC,CAAC,CAAC,EAAExE,SAAS,CAAC4E,IAAI,EAAE5E,SAAS,CAACoE,MAAM,CAAC,CAAC;EACvJ;AACF;AACA;AACA;EACEnD,OAAO,EAAEjB,SAAS,CAAC,sCAAsC0E,SAAS,CAAC,CAAC1E,SAAS,CAAC6E,KAAK,CAAC,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC,EAAE7E,SAAS,CAACqE,MAAM,CAAC;AACnI,CAAC,GAAG,KAAK,CAAC;AACV,eAAenB,KAAK", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}