from flask import Flask, request, jsonify, send_from_directory
from flask_cors import CORS
import pandas as pd
import numpy as np
import os
import logging
from datetime import datetime
import traceback
from werkzeug.utils import secure_filename
import json

# Import our custom modules
from config import Config
from models.data_processor import DataProcessor
from models.powerbi_processor import PowerBIProcessor
from models.model_trainer import ModelTrainer
from models.predictor import PumpTimePredictor
from models.feature_engineer import FeatureEngineer
from utils.validators import DataValidator
from utils.visualizations import VisualizationGenerator
from utils.model_explainer import ModelExplainer
from utils.data_mapper import DataMapper
from utils.feature_manager import FeatureManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)
app.config.from_object(Config)
CORS(app)

# Create necessary directories
os.makedirs(app.config['UPLOAD_FOLDER'], exist_ok=True)
os.makedirs(app.config['MODEL_STORAGE_PATH'], exist_ok=True)

# Initialize components
data_processor = DataProcessor()
powerbi_processor = PowerBIProcessor()
data_mapper = DataMapper()
feature_manager = FeatureManager()
model_trainer = ModelTrainer()
predictor = PumpTimePredictor()
feature_engineer = FeatureEngineer()
validator = DataValidator()
viz_generator = VisualizationGenerator()
model_explainer = ModelExplainer()

# Global variables to store current session data
current_data = None
current_model_results = None

def convert_numpy_types(obj):
    """Convert NumPy types to native Python types for JSON serialization"""
    if isinstance(obj, np.integer):
        return int(obj)
    elif isinstance(obj, np.floating):
        return float(obj)
    elif isinstance(obj, np.ndarray):
        return obj.tolist()
    elif isinstance(obj, dict):
        return {key: convert_numpy_types(value) for key, value in obj.items()}
    elif isinstance(obj, list):
        return [convert_numpy_types(item) for item in obj]
    else:
        return obj

def allowed_file(filename):
    """Check if file extension is allowed"""
    return '.' in filename and \
           filename.rsplit('.', 1)[1].lower() in app.config['ALLOWED_EXTENSIONS']

@app.route('/')
def index():
    """Serve the main application page"""
    return jsonify({
        'message': 'Pump Time Model API',
        'version': '1.0.0',
        'endpoints': {
            'upload': '/api/upload',
            'validate': '/api/validate',
            'process': '/api/process',
            'train': '/api/train',
            'predict': '/api/predict',
            'explain': '/api/explain',
            'visualize': '/api/visualize'
        }
    })

@app.route('/api/upload', methods=['POST'])
def upload_file():
    """Handle file upload and initial validation"""
    try:
        if 'file' not in request.files:
            return jsonify({'error': 'No file provided'}), 400
        
        file = request.files['file']
        if file.filename == '':
            return jsonify({'error': 'No file selected'}), 400
        
        if not allowed_file(file.filename):
            return jsonify({'error': 'Invalid file type. Please upload Excel files only.'}), 400
        
        # Save the uploaded file
        filename = secure_filename(file.filename)
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"{timestamp}_{filename}"
        filepath = os.path.join(app.config['UPLOAD_FOLDER'], filename)
        file.save(filepath)
        
        # Validate file format
        validation_result = validator.validate_file_format(filepath)
        
        if validation_result['is_valid']:
            # Load and store the data globally
            global current_data
            current_data = data_processor.load_excel_data(filepath)

            # Detect data format and provide format-specific information
            data_format = data_processor.detect_data_format(current_data)

            format_info = {
                'data_format': data_format,
                'shape': current_data.shape,
                'columns': list(current_data.columns)
            }

            if data_format == 'powerbi':
                # Validate PowerBI data specifically
                powerbi_validation = powerbi_processor.validate_powerbi_data(current_data)
                format_info['powerbi_validation'] = powerbi_validation
                format_info['available_targets'] = powerbi_processor.get_available_targets(
                    powerbi_processor.convert_powerbi_to_standard(current_data)
                )

            return jsonify({
                'success': True,
                'filename': filename,
                'filepath': filepath,
                'file_info': validation_result['file_info'],
                'format_info': format_info,
                'message': f'File uploaded successfully. Detected format: {data_format}'
            })
        else:
            return jsonify({
                'success': False,
                'errors': validation_result['errors']
            }), 400
    
    except Exception as e:
        logger.error(f"Error in file upload: {str(e)}")
        return jsonify({'error': f'Upload failed: {str(e)}'}), 500

@app.route('/api/validate', methods=['POST'])
def validate_data():
    """Comprehensive data validation"""
    try:
        global current_data
        if current_data is None:
            return jsonify({'error': 'No data loaded. Please upload a file first.'}), 400

        # Detect data format
        data_format = data_processor.detect_data_format(current_data)

        # Run format-specific validation
        if data_format == 'powerbi':
            # PowerBI-specific validation
            validation_result = powerbi_processor.validate_powerbi_data(current_data)

            # Convert to standard format for analysis
            converted_data = powerbi_processor.convert_powerbi_to_standard(current_data)

            # Get feature summary using data mapper
            feature_summary = data_mapper.create_feature_summary(converted_data)

            # Get available targets
            available_targets = powerbi_processor.get_available_targets(converted_data)

            return jsonify({
                'validation_result': validation_result,
                'feature_summary': feature_summary,
                'available_targets': available_targets,
                'data_format': data_format,
                'original_shape': current_data.shape,
                'converted_shape': converted_data.shape,
                'recommendations': validation_result.get('recommendations', [])
            })
        else:
            # Standard validation
            validation_result = validator.comprehensive_validation(df=current_data)
            quality_report = data_processor.generate_data_quality_report(current_data)
            cleaning_suggestions = validator.get_data_cleaning_suggestions(validation_result)

            return jsonify({
                'validation_result': validation_result,
                'quality_report': quality_report,
                'cleaning_suggestions': cleaning_suggestions,
                'data_format': data_format,
                'data_shape': current_data.shape
            })

    except Exception as e:
        logger.error(f"Error in data validation: {str(e)}")
        return jsonify({'error': f'Validation failed: {str(e)}'}), 500

@app.route('/api/process', methods=['POST'])
def process_data():
    """Process and clean the data"""
    try:
        global current_data
        if current_data is None:
            return jsonify({'error': 'No data loaded. Please upload a file first.'}), 400

        # Get processing options from request
        options = request.get_json() or {}
        cleaning_options = options.get('cleaning_options', {})
        feature_engineering_options = options.get('feature_engineering', {})

        # Detect data format and process accordingly
        data_format = data_processor.detect_data_format(current_data)
        original_shape = current_data.shape

        if data_format == 'powerbi':
            # Convert PowerBI format to standard format
            logger.info("Processing PowerBI export data...")
            converted_data = powerbi_processor.convert_powerbi_to_standard(current_data)

            # Apply PowerBI-specific feature engineering
            processed_data = powerbi_processor.create_modeling_features(converted_data)

            # Store the processed data
            current_data = processed_data

            processing_summary = {
                'data_format': 'powerbi',
                'original_shape': original_shape,
                'converted_shape': converted_data.shape,
                'final_shape': processed_data.shape,
                'columns_after_processing': list(processed_data.columns),
                'powerbi_processing_applied': True,
                'available_targets': powerbi_processor.get_available_targets(processed_data)
            }
        else:
            # Standard processing
            cleaned_data = data_processor.clean_data(current_data, cleaning_options)

            # Apply feature engineering if requested
            if feature_engineering_options.get('apply_feature_engineering', False):
                target_column = feature_engineering_options.get('target_column', 'pump_time')
                include_polynomial = feature_engineering_options.get('include_polynomial', False)
                feature_selection_k = feature_engineering_options.get('feature_selection_k', 20)

                engineered_data, engineering_summary = feature_engineer.apply_all_engineering(
                    cleaned_data, target_column, include_polynomial, feature_selection_k
                )
                current_data = engineered_data
            else:
                current_data = cleaned_data
                engineering_summary = None

            processing_summary = {
                'data_format': 'standard',
                'original_shape': original_shape,
                'final_shape': current_data.shape,
                'columns_after_processing': list(current_data.columns),
                'feature_engineering_applied': feature_engineering_options.get('apply_feature_engineering', False),
                'engineering_summary': engineering_summary
            }

        return jsonify({
            'success': True,
            'processing_summary': processing_summary,
            'message': f'Data processed successfully ({data_format} format)'
        })

    except Exception as e:
        logger.error(f"Error in data processing: {str(e)}")
        return jsonify({'error': f'Data processing failed: {str(e)}'}), 500

@app.route('/api/train', methods=['POST'])
def train_models():
    """Train machine learning models"""
    try:
        global current_data, current_model_results
        if current_data is None:
            return jsonify({'error': 'No data loaded. Please upload and process data first.'}), 400
        
        # Get training options from request
        options = request.get_json() or {}
        target_column = options.get('target_column', 'pump_time')
        algorithms = options.get('algorithms', list(model_trainer.algorithms.keys()))
        hyperparameter_tuning = options.get('hyperparameter_tuning', True)
        test_size = options.get('test_size', 0.2)
        cv_folds = options.get('cv_folds', 5)
        
        # Validate target column
        if target_column not in current_data.columns:
            return jsonify({'error': f'Target column "{target_column}" not found in data'}), 400

        # Validate modeling readiness with detailed debugging
        logger.info(f"Validating modeling readiness for target: {target_column}")
        logger.info(f"Current data shape: {current_data.shape}")
        logger.info(f"Target column exists: {target_column in current_data.columns}")

        if target_column in current_data.columns:
            target_missing = current_data[target_column].isnull().sum()
            target_total = len(current_data)
            target_missing_pct = target_missing / target_total
            logger.info(f"Target missing values: {target_missing}/{target_total} ({target_missing_pct:.1%})")

            # Show some target statistics
            if current_data[target_column].notna().sum() > 0:
                target_stats = current_data[target_column].describe()
                logger.info(f"Target statistics: min={target_stats['min']:.1f}, max={target_stats['max']:.1f}, mean={target_stats['mean']:.1f}")

        modeling_validation = data_mapper.validate_modeling_readiness(current_data, target_column)
        logger.info(f"Modeling validation result: {modeling_validation}")

        if not modeling_validation['is_ready']:
            return jsonify({
                'error': 'Data not ready for modeling',
                'issues': modeling_validation['issues'],
                'warnings': modeling_validation['warnings'],
                'recommendations': modeling_validation.get('recommendations', []),
                'debug_info': {
                    'data_shape': current_data.shape,
                    'target_column': target_column,
                    'target_exists': target_column in current_data.columns,
                    'target_missing_pct': target_missing_pct if target_column in current_data.columns else 'N/A'
                }
            }), 400

        # Prepare features for modeling using data mapper
        X, y, preprocessing_info = data_mapper.prepare_for_modeling(current_data, target_column)
        
        # Split data
        from sklearn.model_selection import train_test_split
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=test_size, random_state=42
        )
        
        # Train models
        training_results = model_trainer.train_multiple_models(
            X_train, y_train, algorithms, hyperparameter_tuning, cv_folds
        )
        
        # Evaluate models
        evaluation_results = model_trainer.evaluate_models(X_test, y_test)
        
        # Get model comparison
        comparison_df = model_trainer.get_model_comparison()
        
        # Store results globally (without non-serializable objects)
        current_model_results = {
            'evaluation_results': evaluation_results,
            'model_performance': evaluation_results,  # For visualization compatibility
            'model_comparison': comparison_df.to_dict('records') if not comparison_df.empty else [],
            'best_model': model_trainer.best_model_name,
            'target_column': target_column,
            'preprocessing_info': preprocessing_info,
            'y_test': y_test.tolist(),
            'y_pred': model_trainer.predict(X_test).tolist() if model_trainer.best_model else None
        }
        
        # Setup model explainer for the best model
        if model_trainer.best_model:
            model_explainer.setup_explainers(
                model_trainer.best_model, 
                X_train, 
                model_trainer.best_model_name
            )
        
        # Prepare serializable training results
        serializable_training_results = {}
        for algorithm, result in training_results.items():
            if 'error' in result:
                serializable_training_results[algorithm] = {'error': result['error']}
            else:
                serializable_training_results[algorithm] = {
                    'best_params': result.get('best_params', {}),
                    'cv_score': result.get('cv_score', 0),
                    'feature_importance': result.get('feature_importance', {})
                }

        # Convert all NumPy types to native Python types for JSON serialization
        response_data = {
            'success': True,
            'training_results': convert_numpy_types(serializable_training_results),
            'evaluation_results': convert_numpy_types(evaluation_results),
            'model_comparison': convert_numpy_types(current_model_results['model_comparison']),
            'best_model': model_trainer.best_model_name,
            'model_summary': {
                'total_models_trained': len(model_trainer.models),
                'best_model': model_trainer.best_model_name,
                'models_available': list(model_trainer.models.keys())
            }
        }

        return jsonify(response_data)
    
    except Exception as e:
        logger.error(f"Error in model training: {str(e)}")
        logger.error(traceback.format_exc())
        return jsonify({'error': f'Model training failed: {str(e)}'}), 500

@app.route('/api/predict', methods=['POST'])
def make_prediction():
    """Make predictions using trained models"""
    try:
        if current_model_results is None or model_trainer.best_model is None:
            return jsonify({'error': 'No trained model available. Please train a model first.'}), 400

        # Get prediction input from request
        prediction_input = request.get_json()

        # Debug logging
        logger.info(f"Received prediction request. Content-Type: {request.content_type}")
        logger.info(f"Request data: {request.data}")
        logger.info(f"Parsed JSON: {prediction_input}")

        if prediction_input is None:
            return jsonify({'error': 'Invalid JSON in request body'}), 400

        if 'input_data' not in prediction_input:
            return jsonify({'error': 'No input data provided'}), 400
        
        input_data = prediction_input['input_data']
        model_name = prediction_input.get('model_name', model_trainer.best_model_name)

        # Check if this is a batch prediction (array of inputs) or single prediction
        is_batch = isinstance(input_data, list)
        logger.info(f"Prediction type: {'batch' if is_batch else 'single'}")

        if is_batch:
            # Handle batch predictions
            prediction_results = []
            for i, single_input in enumerate(input_data):
                try:
                    result = process_single_prediction(single_input, model_name)
                    prediction_results.append(result)
                except Exception as e:
                    logger.error(f"Error in batch prediction {i}: {str(e)}")
                    prediction_results.append({
                        'error': f'Prediction {i+1} failed: {str(e)}',
                        'input': single_input
                    })

            return jsonify({
                'success': True,
                'prediction_results': prediction_results,
                'batch_size': len(input_data)
            })
        else:
            # Handle single prediction
            prediction_result = process_single_prediction(input_data, model_name)
            return jsonify({
                'success': True,
                'prediction_result': prediction_result
            })

    except Exception as e:
        logger.error(f"Error in prediction: {str(e)}")
        return jsonify({'error': f'Prediction failed: {str(e)}'}), 500

def process_single_prediction(input_data, model_name):
    """Process a single prediction request"""
    try:
        # Use the same preprocessing pipeline as training
        model = model_trainer.models[model_name]

        # Get the preprocessing info from training
        target_column = current_model_results.get('target_column', 'pump_time')
        stored_preprocessing_info = current_model_results.get('preprocessing_info', {})
        feature_columns = stored_preprocessing_info.get('feature_columns', [])

        if not feature_columns:
            raise ValueError('No feature information available from training. Please retrain the model.')

        logger.info(f"Making prediction with {len(feature_columns)} features: {feature_columns}")

        # Create a DataFrame with the input data
        input_df = pd.DataFrame([input_data])

        # Add default values for missing features that might be needed
        # Based on the training data structure
        if 'product_category' not in input_df.columns:
                # Infer product category from product type
                product_type = input_data.get('product_type', 'Unknown')
                if any(fuel in product_type.lower() for fuel in ['gasoline', 'petrol']):
                    input_df['product_category'] = 'gasoline'
                elif any(fuel in product_type.lower() for fuel in ['diesel', 'gasoil']):
                    input_df['product_category'] = 'diesel'
                elif 'crude' in product_type.lower():
                    input_df['product_category'] = 'crude'
                elif any(fuel in product_type.lower() for fuel in ['jet', 'kerosene']):
                    input_df['product_category'] = 'jet_fuel'
                else:
                    input_df['product_category'] = 'other'

            # Add temporal features with defaults
        if 'day_of_week' not in input_df.columns:
            input_df['day_of_week'] = 1  # Monday
        if 'is_weekend' not in input_df.columns:
            input_df['is_weekend'] = 0
        if 'hour_of_day' not in input_df.columns:
            input_df['hour_of_day'] = 12  # Noon
        if 'is_business_hours' not in input_df.columns:
            input_df['is_business_hours'] = 1

        # Add default values for other features that might be needed
        if 'vessel_name' not in input_df.columns:
            input_df['vessel_name'] = 'Unknown'
        if 'customer_name' not in input_df.columns:
            input_df['customer_name'] = 'Unknown'
        if 'location' not in input_df.columns:
            input_df['location'] = 'Unknown'

        # Ensure we have all required features
        for feature in feature_columns:
            if feature not in input_df.columns:
                # Add default values for missing features
                if feature in ['day_of_week', 'hour_of_day', 'month']:
                    input_df[feature] = 1
                elif feature in ['is_weekend', 'is_business_hours']:
                    input_df[feature] = 0
                else:
                    input_df[feature] = 'Unknown'

        # Add the target column temporarily (will be removed during preprocessing)
        input_df[target_column] = 0  # Dummy value

        # Select only the features that were used in training
        feature_df = input_df[feature_columns + [target_column]]

        logger.info(f"Feature DataFrame shape: {feature_df.shape}")
        logger.info(f"Feature DataFrame columns: {list(feature_df.columns)}")

        # Apply the same preprocessing as training
        encoding_info = stored_preprocessing_info.get('encoding_info', {})
        scaling_info = stored_preprocessing_info.get('scaling_info', {})

        # Encode categorical variables using stored mappings
        for col in feature_columns:
            if col in encoding_info:
                mapping = encoding_info[col]['mapping']
                # Handle unknown categories
                if feature_df[col].iloc[0] not in mapping:
                    # Use the first category as default
                    feature_df[col] = list(mapping.keys())[0]
                feature_df[col] = feature_df[col].map(mapping).fillna(0)

        # Scale numerical variables using stored scaling info
        for col in feature_columns:
            if col in scaling_info:
                scaling = scaling_info[col]
                if scaling['type'] == 'robust_scaling':
                    feature_df[col] = (feature_df[col] - scaling['median']) / scaling['iqr']
                elif scaling['type'] == 'centering':
                    feature_df[col] = feature_df[col] - scaling['median']

        # Select only the feature columns (exclude target)
        X_processed = feature_df[feature_columns]

        logger.info(f"Processed features shape: {X_processed.shape}")
        logger.info(f"Feature values: {X_processed.iloc[0].to_dict()}")

        # Make prediction using the processed features
        raw_prediction = model.predict(X_processed)[0]

        # Apply reasonable bounds based on target type
        if 'pump' in target_column:
            prediction = max(30, min(600, raw_prediction))  # Pump times: 30-600 minutes
        elif 'pre_pump' in target_column:
            prediction = max(10, min(120, raw_prediction))  # Pre-pump: 10-120 minutes
        elif 'post_pump' in target_column:
            prediction = max(5, min(60, raw_prediction))    # Post-pump: 5-60 minutes
        else:
            prediction = max(30, min(1000, raw_prediction)) # Terminal time: 30-1000 minutes

        return {
            'prediction': float(prediction),
            'raw_prediction': float(raw_prediction),
            'model_used': model_name,
            'target_column': target_column,
            'input_features': input_data,
            'processed_features': feature_columns,
            'feature_count': len(feature_columns),
            'note': f'Prediction bounded for {target_column} realism'
        }

    except Exception as e:
        logger.error(f"Error in single prediction: {str(e)}")
        raise e

@app.route('/api/explain', methods=['POST'])
def explain_prediction():
    """Generate explanations for predictions"""
    try:
        if current_model_results is None or model_trainer.best_model is None:
            return jsonify({'error': 'No trained model available. Please train a model first.'}), 400

        # Get explanation request
        explanation_request = request.get_json()

        if 'input_data' not in explanation_request:
            return jsonify({'error': 'No input data provided for explanation'}), 400

        input_data = explanation_request['input_data']
        explanation_type = explanation_request.get('type', 'comprehensive')

        # Convert input to DataFrame
        df_input = pd.DataFrame([input_data])

        # Preprocess input (same as training data)
        X_processed, _ = data_processor.prepare_features_for_modeling(
            df_input, current_model_results['target_column']
        )

        # Generate explanations
        if explanation_type == 'comprehensive':
            explanation_result = model_explainer.explain_prediction_comprehensive(X_processed)
        elif explanation_type == 'shap':
            explanation_result = model_explainer.explain_prediction_shap(X_processed, 'local')
        elif explanation_type == 'lime':
            explanation_result = model_explainer.explain_prediction_lime(X_processed)
        else:
            return jsonify({'error': f'Unknown explanation type: {explanation_type}'}), 400

        return jsonify({
            'success': True,
            'explanation': explanation_result,
            'input_data': input_data
        })

    except Exception as e:
        logger.error(f"Error in explanation: {str(e)}")
        return jsonify({'error': f'Explanation failed: {str(e)}'}), 500

@app.route('/api/visualize', methods=['POST'])
def generate_visualizations():
    """Generate visualizations for data analysis and model performance"""
    try:
        visualization_request = request.get_json() or {}
        viz_type = visualization_request.get('type', 'data_overview')

        if viz_type == 'data_overview':
            if current_data is None:
                return jsonify({'error': 'No data loaded'}), 400

            dashboard = viz_generator.create_data_overview_dashboard(current_data)
            return jsonify({'success': True, 'dashboard': dashboard})

        elif viz_type == 'target_analysis':
            if current_data is None:
                return jsonify({'error': 'No data loaded'}), 400

            target_columns = visualization_request.get('target_columns')
            analysis = viz_generator.create_target_variable_analysis(current_data, target_columns)
            return jsonify({'success': True, 'analysis': analysis})

        elif viz_type == 'feature_analysis':
            if current_data is None:
                return jsonify({'error': 'No data loaded'}), 400

            analysis = viz_generator.create_feature_analysis(current_data)
            return jsonify({'success': True, 'analysis': analysis})

        elif viz_type == 'relationship_analysis':
            if current_data is None:
                return jsonify({'error': 'No data loaded'}), 400

            target_column = visualization_request.get('target_column', 'pump_time')
            analysis = viz_generator.create_relationship_analysis(current_data, target_column)
            return jsonify({'success': True, 'analysis': analysis})

        elif viz_type == 'model_performance':
            if current_model_results is None:
                return jsonify({'error': 'No model results available'}), 400

            # Convert lists back to numpy arrays for visualization
            y_test = np.array(current_model_results.get('y_test', [])) if current_model_results.get('y_test') else None
            y_pred = np.array(current_model_results.get('y_pred', [])) if current_model_results.get('y_pred') else None

            dashboard = viz_generator.create_model_performance_dashboard(
                current_model_results,
                y_test,
                y_pred
            )
            return jsonify({'success': True, 'dashboard': dashboard})

        elif viz_type == 'feature_importance':
            if current_model_results is None or model_trainer.best_model_name is None:
                return jsonify({'error': 'No trained model available'}), 400

            feature_importance = model_trainer.feature_importance.get(model_trainer.best_model_name, {})
            chart = viz_generator.create_feature_importance_chart(feature_importance)
            return jsonify({'success': True, 'chart': chart})

        elif viz_type == 'prediction_scatter':
            if current_model_results is None:
                return jsonify({'error': 'No model results available'}), 400

            # Convert lists back to numpy arrays for visualization
            y_test = np.array(current_model_results.get('y_test', [])) if current_model_results.get('y_test') else None
            y_pred = np.array(current_model_results.get('y_pred', [])) if current_model_results.get('y_pred') else None

            if y_test is None or y_pred is None:
                return jsonify({'error': 'No prediction data available'}), 400

            # Create the dashboard to get the prediction scatter plot
            dashboard = viz_generator.create_model_performance_dashboard(
                current_model_results, y_test, y_pred
            )

            if 'prediction_scatter' in dashboard:
                # Convert Plotly JSON to base64 image
                chart_json = dashboard['prediction_scatter']
                image_base64 = viz_generator.export_chart_as_image(chart_json)

                if image_base64:
                    return jsonify({'success': True, 'image': image_base64})
                else:
                    # Fallback: return the Plotly JSON for frontend to render
                    return jsonify({'success': True, 'chart': chart_json, 'type': 'plotly'})
            else:
                return jsonify({'error': 'Prediction scatter plot not available'}), 400

        elif viz_type == 'residuals_plot':
            if current_model_results is None:
                return jsonify({'error': 'No model results available'}), 400

            # Convert lists back to numpy arrays for visualization
            y_test = np.array(current_model_results.get('y_test', [])) if current_model_results.get('y_test') else None
            y_pred = np.array(current_model_results.get('y_pred', [])) if current_model_results.get('y_pred') else None

            if y_test is None or y_pred is None:
                return jsonify({'error': 'No prediction data available'}), 400

            # Create the dashboard to get the residuals plot
            dashboard = viz_generator.create_model_performance_dashboard(
                current_model_results, y_test, y_pred
            )

            if 'residuals_plot' in dashboard:
                # Convert Plotly JSON to base64 image
                chart_json = dashboard['residuals_plot']
                image_base64 = viz_generator.export_chart_as_image(chart_json)

                if image_base64:
                    return jsonify({'success': True, 'image': image_base64})
                else:
                    # Fallback: return the Plotly JSON for frontend to render
                    return jsonify({'success': True, 'chart': chart_json, 'type': 'plotly'})
            else:
                return jsonify({'error': 'Residuals plot not available'}), 400

        else:
            return jsonify({'error': f'Unknown visualization type: {viz_type}'}), 400

    except Exception as e:
        logger.error(f"Error in visualization: {str(e)}")
        return jsonify({'error': f'Visualization failed: {str(e)}'}), 500

@app.route('/api/save_model', methods=['POST'])
def save_model():
    """Save trained model to disk"""
    try:
        if current_model_results is None or model_trainer.best_model is None:
            return jsonify({'error': 'No trained model available to save'}), 400

        save_request = request.get_json() or {}
        model_name = save_request.get('model_name', model_trainer.best_model_name)

        if model_name not in model_trainer.models:
            return jsonify({'error': f'Model {model_name} not found'}), 400

        # Save the model
        saved_path = model_trainer.save_model(
            model_name,
            app.config['MODEL_STORAGE_PATH'],
            include_metadata=True
        )

        return jsonify({
            'success': True,
            'saved_path': saved_path,
            'model_name': model_name,
            'message': 'Model saved successfully'
        })

    except Exception as e:
        logger.error(f"Error saving model: {str(e)}")
        return jsonify({'error': f'Model save failed: {str(e)}'}), 500

@app.route('/api/load_model', methods=['POST'])
def load_model():
    """Load a saved model from disk"""
    try:
        load_request = request.get_json()

        if 'model_path' not in load_request:
            return jsonify({'error': 'No model path provided'}), 400

        model_path = load_request['model_path']

        # Load the model
        model_package = model_trainer.load_model(model_path)

        return jsonify({
            'success': True,
            'model_info': {
                'algorithm': model_package.get('algorithm'),
                'timestamp': model_package.get('timestamp'),
                'performance': model_package.get('performance', {})
            },
            'message': 'Model loaded successfully'
        })

    except Exception as e:
        logger.error(f"Error loading model: {str(e)}")
        return jsonify({'error': f'Model load failed: {str(e)}'}), 500

@app.route('/api/features/analyze', methods=['POST'])
def analyze_features():
    """Analyze features for user selection and insights"""
    try:
        global current_data
        if current_data is None:
            return jsonify({'error': 'No data loaded. Please upload a file first.'}), 400

        # Analyze features using feature manager
        analysis = feature_manager.analyze_features(current_data)

        return jsonify({
            'success': True,
            'analysis': analysis,
            'message': 'Feature analysis completed successfully'
        })

    except Exception as e:
        logger.error(f"Error in feature analysis: {str(e)}")
        return jsonify({'error': f'Feature analysis failed: {str(e)}'}), 500

@app.route('/api/features/select', methods=['POST'])
def apply_feature_selection():
    """Apply user-selected features and transformations"""
    try:
        global current_data
        if current_data is None:
            return jsonify({'error': 'No data loaded. Please upload a file first.'}), 400

        selection_request = request.get_json()

        if 'selected_features' not in selection_request:
            return jsonify({'error': 'No features selected'}), 400

        selected_features = selection_request['selected_features']
        transformations = selection_request.get('transformations', {})

        # Validate selected features exist
        missing_features = [f for f in selected_features if f not in current_data.columns]
        if missing_features:
            return jsonify({'error': f'Features not found: {missing_features}'}), 400

        # Apply feature selection and transformations
        processed_data = feature_manager.apply_feature_selection(
            current_data, selected_features, transformations
        )

        # Update current data
        current_data = processed_data

        return jsonify({
            'success': True,
            'processed_shape': processed_data.shape,
            'selected_features': selected_features,
            'transformations_applied': list(transformations.keys()),
            'message': f'Feature selection applied: {len(selected_features)} features selected'
        })

    except Exception as e:
        logger.error(f"Error in feature selection: {str(e)}")
        return jsonify({'error': f'Feature selection failed: {str(e)}'}), 500

@app.route('/api/features/statistics', methods=['POST'])
def get_feature_statistics():
    """Get detailed statistics for specific features"""
    try:
        global current_data
        if current_data is None:
            return jsonify({'error': 'No data loaded. Please upload a file first.'}), 400

        stats_request = request.get_json() or {}
        features = stats_request.get('features', list(current_data.columns))

        # Limit to existing features
        features = [f for f in features if f in current_data.columns]

        detailed_stats = {}
        for feature in features:
            feature_data = current_data[feature]

            stats = {
                'name': feature,
                'dtype': str(feature_data.dtype),
                'count': int(feature_data.count()),
                'missing': int(feature_data.isnull().sum()),
                'missing_percentage': round(feature_data.isnull().sum() / len(current_data) * 100, 2),
                'unique': int(feature_data.nunique())
            }

            if pd.api.types.is_numeric_dtype(feature_data):
                # Numeric statistics
                valid_data = feature_data.dropna()
                if len(valid_data) > 0:
                    stats.update({
                        'mean': float(valid_data.mean()),
                        'median': float(valid_data.median()),
                        'std': float(valid_data.std()),
                        'min': float(valid_data.min()),
                        'max': float(valid_data.max()),
                        'q25': float(valid_data.quantile(0.25)),
                        'q75': float(valid_data.quantile(0.75)),
                        'skewness': float(valid_data.skew()) if valid_data.std() > 0 else 0
                    })

                    # Outlier detection
                    q1, q3 = stats['q25'], stats['q75']
                    iqr = q3 - q1
                    lower_bound = q1 - 1.5 * iqr
                    upper_bound = q3 + 1.5 * iqr
                    outliers = valid_data[(valid_data < lower_bound) | (valid_data > upper_bound)]
                    stats['outliers'] = len(outliers)
                    stats['outlier_percentage'] = round(len(outliers) / len(valid_data) * 100, 2)
            else:
                # Categorical statistics
                value_counts = feature_data.value_counts()
                stats.update({
                    'most_common': value_counts.index[0] if len(value_counts) > 0 else None,
                    'most_common_count': int(value_counts.iloc[0]) if len(value_counts) > 0 else 0,
                    'most_common_percentage': round(value_counts.iloc[0] / feature_data.count() * 100, 2) if len(value_counts) > 0 else 0,
                    'top_values': value_counts.head(5).to_dict()
                })

            detailed_stats[feature] = stats

        return jsonify({
            'success': True,
            'statistics': detailed_stats,
            'total_features': len(features)
        })

    except Exception as e:
        logger.error(f"Error getting feature statistics: {str(e)}")
        return jsonify({'error': f'Feature statistics failed: {str(e)}'}), 500

@app.route('/api/sample-data', methods=['POST'])
def generate_sample_data():
    """Generate sample data for testing"""
    try:
        global current_data

        # Generate simple sample data
        import random
        import pandas as pd
        from datetime import datetime, timedelta

        data = []
        for i in range(200):  # More data for better training
            # Create realistic relationships
            operation_type = random.choice(['Loading', 'Discharging'])
            vessel_type = random.choice(['Vessel', 'Barge'])
            product_type = random.choice(['Gasoline', 'Diesel', 'Crude Oil', 'Jet Fuel'])

            # Base quantity depends on vessel type
            if vessel_type == 'Vessel':
                base_quantity = random.uniform(12000, 25000)  # Vessels handle more
            else:
                base_quantity = random.uniform(5000, 15000)   # Barges handle less

            # Pump time has realistic relationship to quantity
            base_rate = random.uniform(80, 120)  # units per minute
            pump_time = (base_quantity / base_rate) + random.uniform(-30, 30)  # Add some noise
            pump_time = max(30, pump_time)  # Minimum 30 minutes

            # Pre-pump time depends on operation and vessel type
            if operation_type == 'Loading':
                pre_pump_base = 45 if vessel_type == 'Vessel' else 35
            else:
                pre_pump_base = 35 if vessel_type == 'Vessel' else 25

            pre_pump_time = pre_pump_base + random.uniform(-15, 15)
            pre_pump_time = max(10, pre_pump_time)

            # Post-pump time
            post_pump_time = random.uniform(15, 45)

            record = {
                'operation_type': operation_type,
                'vessel_type': vessel_type,
                'product_type': product_type,
                'product_quantity': round(base_quantity, 2),
                'pump_time': round(pump_time, 1),
                'pre_pump_time': round(pre_pump_time, 1),
                'post_pump_time': round(post_pump_time, 1),
                'hour_of_day': random.randint(6, 22),  # Business hours mostly
                'berth_number': random.randint(1, 8)
            }
            data.append(record)

        current_data = pd.DataFrame(data)

        return jsonify({
            'success': True,
            'message': 'Sample data generated successfully',
            'shape': current_data.shape,
            'columns': list(current_data.columns)
        })

    except Exception as e:
        logger.error(f"Error generating sample data: {str(e)}")
        return jsonify({'error': f'Sample data generation failed: {str(e)}'}), 500

@app.route('/api/status', methods=['GET'])
def get_status():
    """Get current application status"""
    try:
        status = {
            'data_loaded': current_data is not None,
            'data_shape': current_data.shape if current_data is not None else None,
            'models_trained': current_model_results is not None,
            'best_model': model_trainer.best_model_name if current_model_results else None,
            'available_algorithms': list(model_trainer.algorithms.keys())
        }

        if current_model_results:
            status['model_performance'] = current_model_results.get('evaluation_results', {})

        return jsonify(status)

    except Exception as e:
        logger.error(f"Error getting status: {str(e)}")
        return jsonify({'error': f'Status check failed: {str(e)}'}), 500

@app.errorhandler(404)
def not_found(error):
    return jsonify({'error': 'Endpoint not found'}), 404

@app.errorhandler(500)
def internal_error(error):
    return jsonify({'error': 'Internal server error'}), 500

if __name__ == '__main__':
    app.run(debug=app.config['DEBUG'], host='0.0.0.0', port=5000)
