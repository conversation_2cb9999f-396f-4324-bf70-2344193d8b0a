{"name": "pump-time-model-frontend", "version": "1.0.0", "description": "Frontend for Vessel/Barge Handling Time Prediction Application", "private": true, "dependencies": {"@emotion/react": "^11.10.6", "@emotion/styled": "^11.10.6", "@mui/icons-material": "^5.11.9", "@mui/material": "^5.11.10", "@mui/x-data-grid": "^6.0.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^14.4.3", "axios": "^1.3.4", "date-fns": "^2.29.3", "lodash": "^4.17.21", "plotly.js": "^2.20.0", "plotly.js-dist": "^3.0.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-dropzone": "^14.2.3", "react-hook-form": "^7.43.5", "react-hot-toast": "^2.4.0", "react-loading-skeleton": "^3.1.1", "react-plotly.js": "^2.6.0", "react-query": "^3.39.3", "react-router-dom": "^6.8.1", "react-scripts": "^5.0.1", "react-step-wizard": "^5.3.11", "recharts": "^2.5.0"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:5000"}