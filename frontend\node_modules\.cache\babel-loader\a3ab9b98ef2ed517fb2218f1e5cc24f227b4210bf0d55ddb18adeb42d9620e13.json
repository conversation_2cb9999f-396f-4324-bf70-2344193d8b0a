{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"values\", \"unit\", \"step\"];\n// Sorted ASC by size. That's important.\n// It can't be configured as it's used statically for propTypes.\nexport const breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\nconst sortBreakpointsValues = values => {\n  const breakpointsAsArray = Object.keys(values).map(key => ({\n    key,\n    val: values[key]\n  })) || [];\n  // Sort in ascending order\n  breakpointsAsArray.sort((breakpoint1, breakpoint2) => breakpoint1.val - breakpoint2.val);\n  return breakpointsAsArray.reduce((acc, obj) => {\n    return _extends({}, acc, {\n      [obj.key]: obj.val\n    });\n  }, {});\n};\n\n// Keep in mind that @media is inclusive by the CSS specification.\nexport default function createBreakpoints(breakpoints) {\n  const {\n      // The breakpoint **start** at this value.\n      // For instance with the first breakpoint xs: [xs, sm).\n      values = {\n        xs: 0,\n        // phone\n        sm: 600,\n        // tablet\n        md: 900,\n        // small laptop\n        lg: 1200,\n        // desktop\n        xl: 1536 // large screen\n      },\n      unit = 'px',\n      step = 5\n    } = breakpoints,\n    other = _objectWithoutPropertiesLoose(breakpoints, _excluded);\n  const sortedValues = sortBreakpointsValues(values);\n  const keys = Object.keys(sortedValues);\n  function up(key) {\n    const value = typeof values[key] === 'number' ? values[key] : key;\n    return `@media (min-width:${value}${unit})`;\n  }\n  function down(key) {\n    const value = typeof values[key] === 'number' ? values[key] : key;\n    return `@media (max-width:${value - step / 100}${unit})`;\n  }\n  function between(start, end) {\n    const endIndex = keys.indexOf(end);\n    return `@media (min-width:${typeof values[start] === 'number' ? values[start] : start}${unit}) and ` + `(max-width:${(endIndex !== -1 && typeof values[keys[endIndex]] === 'number' ? values[keys[endIndex]] : end) - step / 100}${unit})`;\n  }\n  function only(key) {\n    if (keys.indexOf(key) + 1 < keys.length) {\n      return between(key, keys[keys.indexOf(key) + 1]);\n    }\n    return up(key);\n  }\n  function not(key) {\n    // handle first and last key separately, for better readability\n    const keyIndex = keys.indexOf(key);\n    if (keyIndex === 0) {\n      return up(keys[1]);\n    }\n    if (keyIndex === keys.length - 1) {\n      return down(keys[keyIndex]);\n    }\n    return between(key, keys[keys.indexOf(key) + 1]).replace('@media', '@media not all and');\n  }\n  return _extends({\n    keys,\n    values: sortedValues,\n    up,\n    down,\n    between,\n    only,\n    not,\n    unit\n  }, other);\n}", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_extends", "_excluded", "breakpoint<PERSON><PERSON><PERSON>", "sortBreakpointsValues", "values", "breakpointsAsArray", "Object", "keys", "map", "key", "val", "sort", "breakpoint1", "breakpoint2", "reduce", "acc", "obj", "createBreakpoints", "breakpoints", "xs", "sm", "md", "lg", "xl", "unit", "step", "other", "sortedValues", "up", "value", "down", "between", "start", "end", "endIndex", "indexOf", "only", "length", "not", "keyIndex", "replace"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/system/esm/createTheme/createBreakpoints.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nconst _excluded = [\"values\", \"unit\", \"step\"];\n// Sorted ASC by size. That's important.\n// It can't be configured as it's used statically for propTypes.\nexport const breakpointKeys = ['xs', 'sm', 'md', 'lg', 'xl'];\nconst sortBreakpointsValues = values => {\n  const breakpointsAsArray = Object.keys(values).map(key => ({\n    key,\n    val: values[key]\n  })) || [];\n  // Sort in ascending order\n  breakpointsAsArray.sort((breakpoint1, breakpoint2) => breakpoint1.val - breakpoint2.val);\n  return breakpointsAsArray.reduce((acc, obj) => {\n    return _extends({}, acc, {\n      [obj.key]: obj.val\n    });\n  }, {});\n};\n\n// Keep in mind that @media is inclusive by the CSS specification.\nexport default function createBreakpoints(breakpoints) {\n  const {\n      // The breakpoint **start** at this value.\n      // For instance with the first breakpoint xs: [xs, sm).\n      values = {\n        xs: 0,\n        // phone\n        sm: 600,\n        // tablet\n        md: 900,\n        // small laptop\n        lg: 1200,\n        // desktop\n        xl: 1536 // large screen\n      },\n      unit = 'px',\n      step = 5\n    } = breakpoints,\n    other = _objectWithoutPropertiesLoose(breakpoints, _excluded);\n  const sortedValues = sortBreakpointsValues(values);\n  const keys = Object.keys(sortedValues);\n  function up(key) {\n    const value = typeof values[key] === 'number' ? values[key] : key;\n    return `@media (min-width:${value}${unit})`;\n  }\n  function down(key) {\n    const value = typeof values[key] === 'number' ? values[key] : key;\n    return `@media (max-width:${value - step / 100}${unit})`;\n  }\n  function between(start, end) {\n    const endIndex = keys.indexOf(end);\n    return `@media (min-width:${typeof values[start] === 'number' ? values[start] : start}${unit}) and ` + `(max-width:${(endIndex !== -1 && typeof values[keys[endIndex]] === 'number' ? values[keys[endIndex]] : end) - step / 100}${unit})`;\n  }\n  function only(key) {\n    if (keys.indexOf(key) + 1 < keys.length) {\n      return between(key, keys[keys.indexOf(key) + 1]);\n    }\n    return up(key);\n  }\n  function not(key) {\n    // handle first and last key separately, for better readability\n    const keyIndex = keys.indexOf(key);\n    if (keyIndex === 0) {\n      return up(keys[1]);\n    }\n    if (keyIndex === keys.length - 1) {\n      return down(keys[keyIndex]);\n    }\n    return between(key, keys[keys.indexOf(key) + 1]).replace('@media', '@media not all and');\n  }\n  return _extends({\n    keys,\n    values: sortedValues,\n    up,\n    down,\n    between,\n    only,\n    not,\n    unit\n  }, other);\n}"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,QAAQ,MAAM,oCAAoC;AACzD,MAAMC,SAAS,GAAG,CAAC,QAAQ,EAAE,MAAM,EAAE,MAAM,CAAC;AAC5C;AACA;AACA,OAAO,MAAMC,cAAc,GAAG,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AAC5D,MAAMC,qBAAqB,GAAGC,MAAM,IAAI;EACtC,MAAMC,kBAAkB,GAAGC,MAAM,CAACC,IAAI,CAACH,MAAM,CAAC,CAACI,GAAG,CAACC,GAAG,KAAK;IACzDA,GAAG;IACHC,GAAG,EAAEN,MAAM,CAACK,GAAG;EACjB,CAAC,CAAC,CAAC,IAAI,EAAE;EACT;EACAJ,kBAAkB,CAACM,IAAI,CAAC,CAACC,WAAW,EAAEC,WAAW,KAAKD,WAAW,CAACF,GAAG,GAAGG,WAAW,CAACH,GAAG,CAAC;EACxF,OAAOL,kBAAkB,CAACS,MAAM,CAAC,CAACC,GAAG,EAAEC,GAAG,KAAK;IAC7C,OAAOhB,QAAQ,CAAC,CAAC,CAAC,EAAEe,GAAG,EAAE;MACvB,CAACC,GAAG,CAACP,GAAG,GAAGO,GAAG,CAACN;IACjB,CAAC,CAAC;EACJ,CAAC,EAAE,CAAC,CAAC,CAAC;AACR,CAAC;;AAED;AACA,eAAe,SAASO,iBAAiBA,CAACC,WAAW,EAAE;EACrD,MAAM;MACF;MACA;MACAd,MAAM,GAAG;QACPe,EAAE,EAAE,CAAC;QACL;QACAC,EAAE,EAAE,GAAG;QACP;QACAC,EAAE,EAAE,GAAG;QACP;QACAC,EAAE,EAAE,IAAI;QACR;QACAC,EAAE,EAAE,IAAI,CAAC;MACX,CAAC;MACDC,IAAI,GAAG,IAAI;MACXC,IAAI,GAAG;IACT,CAAC,GAAGP,WAAW;IACfQ,KAAK,GAAG3B,6BAA6B,CAACmB,WAAW,EAAEjB,SAAS,CAAC;EAC/D,MAAM0B,YAAY,GAAGxB,qBAAqB,CAACC,MAAM,CAAC;EAClD,MAAMG,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACoB,YAAY,CAAC;EACtC,SAASC,EAAEA,CAACnB,GAAG,EAAE;IACf,MAAMoB,KAAK,GAAG,OAAOzB,MAAM,CAACK,GAAG,CAAC,KAAK,QAAQ,GAAGL,MAAM,CAACK,GAAG,CAAC,GAAGA,GAAG;IACjE,OAAO,qBAAqBoB,KAAK,GAAGL,IAAI,GAAG;EAC7C;EACA,SAASM,IAAIA,CAACrB,GAAG,EAAE;IACjB,MAAMoB,KAAK,GAAG,OAAOzB,MAAM,CAACK,GAAG,CAAC,KAAK,QAAQ,GAAGL,MAAM,CAACK,GAAG,CAAC,GAAGA,GAAG;IACjE,OAAO,qBAAqBoB,KAAK,GAAGJ,IAAI,GAAG,GAAG,GAAGD,IAAI,GAAG;EAC1D;EACA,SAASO,OAAOA,CAACC,KAAK,EAAEC,GAAG,EAAE;IAC3B,MAAMC,QAAQ,GAAG3B,IAAI,CAAC4B,OAAO,CAACF,GAAG,CAAC;IAClC,OAAO,qBAAqB,OAAO7B,MAAM,CAAC4B,KAAK,CAAC,KAAK,QAAQ,GAAG5B,MAAM,CAAC4B,KAAK,CAAC,GAAGA,KAAK,GAAGR,IAAI,QAAQ,GAAG,cAAc,CAACU,QAAQ,KAAK,CAAC,CAAC,IAAI,OAAO9B,MAAM,CAACG,IAAI,CAAC2B,QAAQ,CAAC,CAAC,KAAK,QAAQ,GAAG9B,MAAM,CAACG,IAAI,CAAC2B,QAAQ,CAAC,CAAC,GAAGD,GAAG,IAAIR,IAAI,GAAG,GAAG,GAAGD,IAAI,GAAG;EAC5O;EACA,SAASY,IAAIA,CAAC3B,GAAG,EAAE;IACjB,IAAIF,IAAI,CAAC4B,OAAO,CAAC1B,GAAG,CAAC,GAAG,CAAC,GAAGF,IAAI,CAAC8B,MAAM,EAAE;MACvC,OAAON,OAAO,CAACtB,GAAG,EAAEF,IAAI,CAACA,IAAI,CAAC4B,OAAO,CAAC1B,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;IAClD;IACA,OAAOmB,EAAE,CAACnB,GAAG,CAAC;EAChB;EACA,SAAS6B,GAAGA,CAAC7B,GAAG,EAAE;IAChB;IACA,MAAM8B,QAAQ,GAAGhC,IAAI,CAAC4B,OAAO,CAAC1B,GAAG,CAAC;IAClC,IAAI8B,QAAQ,KAAK,CAAC,EAAE;MAClB,OAAOX,EAAE,CAACrB,IAAI,CAAC,CAAC,CAAC,CAAC;IACpB;IACA,IAAIgC,QAAQ,KAAKhC,IAAI,CAAC8B,MAAM,GAAG,CAAC,EAAE;MAChC,OAAOP,IAAI,CAACvB,IAAI,CAACgC,QAAQ,CAAC,CAAC;IAC7B;IACA,OAAOR,OAAO,CAACtB,GAAG,EAAEF,IAAI,CAACA,IAAI,CAAC4B,OAAO,CAAC1B,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC+B,OAAO,CAAC,QAAQ,EAAE,oBAAoB,CAAC;EAC1F;EACA,OAAOxC,QAAQ,CAAC;IACdO,IAAI;IACJH,MAAM,EAAEuB,YAAY;IACpBC,EAAE;IACFE,IAAI;IACJC,OAAO;IACPK,IAAI;IACJE,GAAG;IACHd;EACF,CAAC,EAAEE,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}