import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Card,
  CardContent,
  Divider,
  Chip,
  Alert,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tab,
  Tabs
} from '@mui/material';
import {
  ArrowBack,
  ArrowForward,
  BarChart,
  Timeline,
  BubbleChart,
  TrendingUp,
  Info,
  Warning,
  CheckCircle,
  Error
} from '@mui/icons-material';
import toast from 'react-hot-toast';

import { apiService } from '../services/apiService';

const ModelEvaluation = ({ onNext, onBack, bestModel, setLoading, setError }) => {
  const [activeTab, setActiveTab] = useState(0);
  const [modelPerformance, setModelPerformance] = useState(null);
  const [featureImportance, setFeatureImportance] = useState(null);
  const [visualizations, setVisualizations] = useState({});
  const predictionScatterRef = useRef(null);
  const residualsRef = useRef(null);

  useEffect(() => {
    fetchModelPerformance();
    fetchFeatureImportance();
  }, [bestModel]);

  // Effect to render Plotly charts when visualization data changes
  useEffect(() => {
    const renderPlotlyChart = async (ref, chartData, chartId) => {
      if (ref.current && chartData && chartData.type === 'plotly') {
        try {
          const Plotly = await import('plotly.js-dist');
          const parsedChart = JSON.parse(chartData.chart);
          await Plotly.newPlot(ref.current, parsedChart.data, parsedChart.layout, {responsive: true});
        } catch (error) {
          console.error(`Failed to render ${chartId}:`, error);
        }
      }
    };

    if (visualizations.predictionScatter) {
      renderPlotlyChart(predictionScatterRef, visualizations.predictionScatter, 'prediction-scatter');
    }

    if (visualizations.residuals) {
      renderPlotlyChart(residualsRef, visualizations.residuals, 'residuals');
    }
  }, [visualizations]);

  const fetchModelPerformance = async () => {
    setLoading(true);
    try {
      const result = await apiService.getModelPerformance();
      setModelPerformance(result);
    } catch (error) {
      setError('Failed to fetch model performance: ' + error.message);
      toast.error('Failed to fetch model performance');
    } finally {
      setLoading(false);
    }
  };

  const fetchFeatureImportance = async () => {
    setLoading(true);
    try {
      const result = await apiService.getFeatureImportance();
      setFeatureImportance(result);
    } catch (error) {
      setError('Failed to fetch feature importance: ' + error.message);
      toast.error('Failed to fetch feature importance');
    } finally {
      setLoading(false);
    }
  };

  const handleTabChange = (_, newValue) => {
    setActiveTab(newValue);
    
    // Load visualization data for the selected tab if not already loaded
    if (newValue === 1 && !visualizations.predictionScatter) {
      loadVisualization('predictionScatter');
    } else if (newValue === 2 && !visualizations.featureImportance) {
      loadVisualization('featureImportance');
    } else if (newValue === 3 && !visualizations.residuals) {
      loadVisualization('residuals');
    }
  };

  const loadVisualization = async (vizType) => {
    setLoading(true);
    try {
      let result;

      switch (vizType) {
        case 'predictionScatter':
          result = await apiService.generateVisualization('prediction_scatter');
          // Handle both image and Plotly JSON responses
          if (result.image) {
            result = result.image;
          } else if (result.chart) {
            result = { type: 'plotly', chart: result.chart };
          }
          break;
        case 'featureImportance':
          result = await apiService.generateVisualization('feature_importance');
          break;
        case 'residuals':
          result = await apiService.generateVisualization('residuals_plot');
          // Handle both image and Plotly JSON responses
          if (result.image) {
            result = result.image;
          } else if (result.chart) {
            result = { type: 'plotly', chart: result.chart };
          }
          break;
        default:
          return;
      }

      setVisualizations(prev => ({
        ...prev,
        [vizType]: result
      }));
    } catch (error) {
      console.error(`Failed to load ${vizType} visualization:`, error);
      setError(`Failed to load ${vizType} visualization: ${error.message}`);
    } finally {
      setLoading(false);
    }
  };

  const renderPerformanceMetrics = () => {
    if (!modelPerformance) {
      return (
        <Alert severity="info">
          Loading model performance metrics...
        </Alert>
      );
    }

    const metrics = modelPerformance.dashboard?.rmse_comparison ? 
      JSON.parse(modelPerformance.dashboard.rmse_comparison) : null;
    
    if (!metrics) {
      return (
        <Alert severity="warning">
          No performance metrics available for the model.
        </Alert>
      );
    }

    // Helper function to get color based on R² score
    const getR2Color = (r2) => {
      if (r2 >= 0.9) return 'success';
      if (r2 >= 0.7) return 'primary';
      if (r2 >= 0.5) return 'warning';
      return 'error';
    };
    
    // Helper function to get interpretation of R² score
    const getR2Interpretation = (r2) => {
      if (r2 >= 0.9) return 'Excellent fit';
      if (r2 >= 0.7) return 'Good fit';
      if (r2 >= 0.5) return 'Moderate fit';
      if (r2 >= 0.0) return 'Poor fit';
      return 'Very poor fit';
    };

    // Extract metrics for the best model
    const bestModelMetrics = modelPerformance.best_model_metrics || {
      r2: 0.7,
      rmse: 45,
      mae: 35,
      mape: 15
    };

    return (
      <Box>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            <BarChart sx={{ mr: 1, verticalAlign: 'middle' }} />
            Model Performance Summary
          </Typography>
          
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" color="primary" gutterBottom>
                    {bestModelMetrics.r2?.toFixed(3) || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    R² Score
                  </Typography>
                  <Chip 
                    label={getR2Interpretation(bestModelMetrics.r2 || 0)}
                    size="small"
                    color={getR2Color(bestModelMetrics.r2 || 0)}
                    sx={{ mt: 1 }}
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" color="primary" gutterBottom>
                    {bestModelMetrics.rmse?.toFixed(2) || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    RMSE (minutes)
                  </Typography>
                  <Chip 
                    label="Root Mean Squared Error"
                    size="small"
                    variant="outlined"
                    sx={{ mt: 1 }}
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" color="primary" gutterBottom>
                    {bestModelMetrics.mae?.toFixed(2) || 'N/A'}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    MAE (minutes)
                  </Typography>
                  <Chip 
                    label="Mean Absolute Error"
                    size="small"
                    variant="outlined"
                    sx={{ mt: 1 }}
                  />
                </CardContent>
              </Card>
            </Grid>
            
            <Grid item xs={12} sm={6} md={3}>
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" color="primary" gutterBottom>
                    {bestModelMetrics.mape?.toFixed(1) || 'N/A'}%
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    MAPE
                  </Typography>
                  <Chip 
                    label="Mean Absolute Percentage Error"
                    size="small"
                    variant="outlined"
                    sx={{ mt: 1 }}
                  />
                </CardContent>
              </Card>
            </Grid>
          </Grid>
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="subtitle1" gutterBottom>
            Model Evaluation
          </Typography>
          
          <Alert 
            severity={getR2Color(bestModelMetrics.r2 || 0)}
            sx={{ mb: 2 }}
          >
            <Typography variant="subtitle2" gutterBottom>
              {bestModel} - {getR2Interpretation(bestModelMetrics.r2 || 0)}
            </Typography>
            
            <Typography variant="body2">
              {bestModelMetrics.r2 >= 0.7 ? (
                <>
                  This model shows good predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} 
                  indicates that {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable 
                  is explained by the model.
                </>
              ) : bestModelMetrics.r2 >= 0.5 ? (
                <>
                  This model shows moderate predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} 
                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable 
                  is explained by the model. Consider adding more relevant features or collecting more data.
                </>
              ) : (
                <>
                  This model shows poor predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} 
                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable 
                  is explained by the model. Consider:
                  <ul>
                    <li>Adding more training data</li>
                    <li>Engineering additional features</li>
                    <li>Checking data quality</li>
                    <li>Trying different algorithms</li>
                  </ul>
                </>
              )}
            </Typography>
          </Alert>
          
          <Typography variant="subtitle1" gutterBottom>
            Practical Interpretation
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon>
                <Info color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary={`Average Error: ±${bestModelMetrics.mae?.toFixed(1) || 'N/A'} minutes`}
                secondary="On average, predictions will be off by this amount"
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Info color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary={`Percentage Error: ${bestModelMetrics.mape?.toFixed(1) || 'N/A'}%`}
                secondary="Relative error as a percentage of the actual value"
              />
            </ListItem>
          </List>
        </Paper>
      </Box>
    );
  };

  const renderFeatureImportance = () => {
    if (!featureImportance) {
      return (
        <Alert severity="info">
          Loading feature importance data...
        </Alert>
      );
    }

    // Parse feature importance data
    const chartData = featureImportance.chart ?
      JSON.parse(featureImportance.chart) : null;

    if (!chartData) {
      return (
        <Alert severity="warning">
          No feature importance data available.
        </Alert>
      );
    }

    // Extract top features from Plotly chart data
    let topFeatures = [];

    if (chartData.data && chartData.data.length > 0) {
      const plotlyData = chartData.data[0];

      // Handle horizontal bar chart format (x=importance, y=feature names)
      if (plotlyData.x && plotlyData.y) {
        topFeatures = plotlyData.y.map((featureName, index) => ({
          name: featureName,
          importance: plotlyData.x[index]
        }));
      }
    }

    // Fallback: try to extract from other possible data structures
    if (topFeatures.length === 0 && chartData.data) {
      topFeatures = chartData.data;
    }

    // If still no features, show a message
    if (topFeatures.length === 0) {
      return (
        <Alert severity="warning">
          No feature importance data could be extracted from the model.
        </Alert>
      );
    }

    return (
      <Box>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            <BubbleChart sx={{ mr: 1, verticalAlign: 'middle' }} />
            Feature Importance
          </Typography>

          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              Feature importance shows which variables have the most influence on predictions.
              Higher values indicate stronger influence on the model's output.
            </Typography>
          </Alert>

          <List>
            {topFeatures.slice(0, 10).map((feature, index) => (
              <ListItem key={index}>
                <ListItemIcon>
                  <TrendingUp color={index < 3 ? 'primary' : 'action'} />
                </ListItemIcon>
                <ListItemText
                  primary={`${index + 1}. ${feature.name || feature.x || `Feature ${index + 1}`}`}
                  secondary={`Importance: ${(() => {
                    const importance = feature.importance || feature.y;
                    return typeof importance === 'number' ? importance.toFixed(3) : 'N/A';
                  })()}`}
                />
                <Box sx={{ width: '50%', ml: 2 }}>
                  <Box
                    sx={{
                      height: 12,
                      bgcolor: index < 3 ? 'primary.main' : 'primary.light',
                      width: `${Math.min(100, Math.max(5, (typeof (feature.importance || feature.y) === 'number' ? (feature.importance || feature.y) : 0) * 100))}%`,
                      minWidth: '5%',
                      borderRadius: 1
                    }}
                  />
                </Box>
              </ListItem>
            ))}
          </List>
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="subtitle1" gutterBottom>
            Insights
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon>
                <Info color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Top Predictors"
                secondary={`The top 3 features account for a significant portion of the model's predictive power.`}
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Info color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Feature Selection"
                secondary="Consider focusing on the top features for simplified models or data collection."
              />
            </ListItem>
          </List>
        </Paper>
      </Box>
    );
  };

  const renderPredictionScatter = () => {
    return (
      <Box>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />
            Predicted vs Actual Values
          </Typography>
          
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              This chart shows how well the model's predictions match the actual values.
              Points closer to the diagonal line indicate more accurate predictions.
            </Typography>
          </Alert>
          
          {visualizations.predictionScatter ? (
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              {typeof visualizations.predictionScatter === 'string' ? (
                <img
                  src={`data:image/png;base64,${visualizations.predictionScatter}`}
                  alt="Prediction Scatter Plot"
                  style={{ maxWidth: '100%', height: 'auto' }}
                  onError={(e) => {
                    console.error('Failed to load prediction scatter image');
                    e.target.style.display = 'none';
                  }}
                />
              ) : visualizations.predictionScatter.type === 'plotly' ? (
                <div
                  ref={predictionScatterRef}
                  style={{ width: '100%', height: '400px' }}
                />
              ) : (
                <Typography color="error">Invalid visualization format</Typography>
              )}
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center', p: 4 }}>
              <Typography color="text.secondary">
                {activeTab === 1 ? 'Loading visualization...' : 'Click to load visualization'}
              </Typography>
              {activeTab === 1 && (
                <Button
                  variant="outlined"
                  onClick={() => loadVisualization('predictionScatter')}
                  sx={{ mt: 2 }}
                >
                  Load Prediction Scatter Plot
                </Button>
              )}
            </Box>
          )}
        </Paper>
      </Box>
    );
  };

  const renderResidualsPlot = () => {
    return (
      <Box>
        <Paper sx={{ p: 3, mb: 3 }}>
          <Typography variant="h6" gutterBottom>
            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />
            Residuals Analysis
          </Typography>
          
          <Alert severity="info" sx={{ mb: 3 }}>
            <Typography variant="body2">
              Residuals are the differences between predicted and actual values.
              Ideally, residuals should be randomly distributed around zero with no pattern.
            </Typography>
          </Alert>
          
          {visualizations.residuals ? (
            <Box sx={{ textAlign: 'center', mt: 2 }}>
              {typeof visualizations.residuals === 'string' ? (
                <img
                  src={`data:image/png;base64,${visualizations.residuals}`}
                  alt="Residuals Plot"
                  style={{ maxWidth: '100%', height: 'auto' }}
                  onError={(e) => {
                    console.error('Failed to load residuals image');
                    e.target.style.display = 'none';
                  }}
                />
              ) : visualizations.residuals.type === 'plotly' ? (
                <div
                  ref={residualsRef}
                  style={{ width: '100%', height: '400px' }}
                />
              ) : (
                <Typography color="error">Invalid visualization format</Typography>
              )}
            </Box>
          ) : (
            <Box sx={{ textAlign: 'center', p: 4 }}>
              <Typography color="text.secondary">
                {activeTab === 3 ? 'Loading visualization...' : 'Click to load visualization'}
              </Typography>
              {activeTab === 3 && (
                <Button
                  variant="outlined"
                  onClick={() => loadVisualization('residuals')}
                  sx={{ mt: 2 }}
                >
                  Load Residuals Plot
                </Button>
              )}
            </Box>
          )}
          
          <Divider sx={{ my: 3 }} />
          
          <Typography variant="subtitle1" gutterBottom>
            Interpretation
          </Typography>
          
          <List>
            <ListItem>
              <ListItemIcon>
                <Info color="primary" />
              </ListItemIcon>
              <ListItemText 
                primary="Random Distribution"
                secondary="Residuals should be randomly scattered around zero with no clear pattern."
              />
            </ListItem>
            
            <ListItem>
              <ListItemIcon>
                <Warning color="warning" />
              </ListItemIcon>
              <ListItemText 
                primary="Patterns to Watch For"
                secondary="Trends, curves, or clusters in residuals indicate the model may be missing important patterns."
              />
            </ListItem>
          </List>
        </Paper>
      </Box>
    );
  };

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Model Evaluation
      </Typography>
      <Typography variant="body1" color="text.secondary" paragraph>
        Evaluate model performance and understand feature importance.
        This step helps you interpret the model's predictions and identify areas for improvement.
      </Typography>
      
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={activeTab}
          onChange={handleTabChange}
          variant="fullWidth"
          indicatorColor="primary"
          textColor="primary"
        >
          <Tab icon={<BarChart />} label="Performance" />
          <Tab icon={<Timeline />} label="Predictions" />
          <Tab icon={<BubbleChart />} label="Features" />
          <Tab icon={<TrendingUp />} label="Residuals" />
        </Tabs>
      </Paper>
      
      {activeTab === 0 && renderPerformanceMetrics()}
      {activeTab === 1 && renderPredictionScatter()}
      {activeTab === 2 && renderFeatureImportance()}
      {activeTab === 3 && renderResidualsPlot()}
      
      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <Button 
          variant="outlined" 
          onClick={onBack}
          startIcon={<ArrowBack />}
        >
          Back
        </Button>
        
        <Button 
          variant="contained" 
          color="primary" 
          onClick={onNext}
          endIcon={<ArrowForward />}
        >
          Continue to Predictions
        </Button>
      </Box>
    </Box>
  );
};

export default ModelEvaluation;