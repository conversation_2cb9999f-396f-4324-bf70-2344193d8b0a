{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Pumptimemodel\\\\frontend\\\\src\\\\components\\\\FeatureManagement.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, CardHeader, CardActions, Divider, Chip, FormControl, InputLabel, Select, MenuItem, Checkbox, FormControlLabel, Alert, LinearProgress, IconButton, Tooltip } from '@mui/material';\nimport { ArrowBack, ArrowForward, CheckCircle, Error, Warning, Info, FilterList, SelectAll, Clear, Tune, Category, Help } from '@mui/icons-material';\nimport toast from 'react-hot-toast';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst FeatureManagement = ({\n  onNext,\n  onBack,\n  setLoading,\n  setError\n}) => {\n  _s();\n  const [featureAnalysis, setFeatureAnalysis] = useState(null);\n  const [selectedFeatures, setSelectedFeatures] = useState(new Set());\n  const [featureTransformations, setFeatureTransformations] = useState({});\n  const [categories, setCategories] = useState({});\n  const [selectionResult, setSelectionResult] = useState(null);\n  const [analyzing, setAnalyzing] = useState(false);\n  useEffect(() => {\n    analyzeFeatures();\n  }, []);\n  const analyzeFeatures = async () => {\n    setAnalyzing(true);\n    setLoading(true);\n    setError(null);\n    try {\n      // Try to get feature analysis from the API\n      let result;\n      try {\n        result = await apiService.getFeatureAnalysis();\n      } catch (apiError) {\n        // If API endpoint doesn't exist, create mock data\n        result = {\n          analysis: {\n            data_quality: {\n              total_features: 8,\n              completeness_percentage: 95,\n              numeric_features: 4,\n              categorical_features: 4\n            },\n            feature_summary: {\n              'product_quantity': {\n                feature_type: 'numeric',\n                data_quality_score: 85,\n                missing_percentage: 0,\n                unique_count: 150,\n                outlier_percentage: 5,\n                recommendation: 'include',\n                description: 'Quantity of product being handled'\n              },\n              'vessel_type': {\n                feature_type: 'categorical',\n                data_quality_score: 90,\n                missing_percentage: 0,\n                unique_count: 2,\n                most_common: 'Vessel',\n                recommendation: 'include',\n                description: 'Type of vessel (Vessel or Barge)'\n              },\n              'operation_type': {\n                feature_type: 'categorical',\n                data_quality_score: 88,\n                missing_percentage: 0,\n                unique_count: 2,\n                most_common: 'Loading',\n                recommendation: 'include',\n                description: 'Type of operation (Loading or Discharging)'\n              },\n              'product_type': {\n                feature_type: 'categorical',\n                data_quality_score: 75,\n                missing_percentage: 2,\n                unique_count: 8,\n                most_common: 'Gasoline',\n                recommendation: 'include',\n                description: 'Type of product being handled'\n              },\n              'hour_of_day': {\n                feature_type: 'numeric',\n                data_quality_score: 80,\n                missing_percentage: 0,\n                unique_count: 24,\n                outlier_percentage: 0,\n                recommendation: 'include',\n                description: 'Hour of day when operation starts'\n              }\n            },\n            categories: {\n              'operational': ['operation_type', 'hour_of_day'],\n              'vessel': ['vessel_type'],\n              'product': ['product_type', 'product_quantity']\n            },\n            recommendations: ['All features show good data quality', 'Consider feature engineering for temporal features', 'Product quantity may benefit from normalization']\n          }\n        };\n      }\n      setFeatureAnalysis(result.analysis);\n\n      // Organize features by category\n      if (result.analysis.categories) {\n        setCategories(result.analysis.categories);\n      }\n\n      // Initialize selected features with recommended ones\n      const recommended = new Set();\n      Object.entries(result.analysis.feature_summary || {}).forEach(([feature, info]) => {\n        if (info.recommendation === 'include') {\n          recommended.add(feature);\n        }\n      });\n      setSelectedFeatures(recommended);\n      toast.success('Feature analysis completed!');\n    } catch (error) {\n      setError(error.message);\n      toast.error('Feature analysis failed: ' + error.message);\n    } finally {\n      setLoading(false);\n      setAnalyzing(false);\n    }\n  };\n  const applyFeatureSelection = async () => {\n    if (selectedFeatures.size === 0) {\n      toast.error('Please select at least one feature');\n      return;\n    }\n    setLoading(true);\n    setError(null);\n    try {\n      // For now, simulate feature selection since the backend endpoint might not exist\n      const result = {\n        success: true,\n        selected_features: Array.from(selectedFeatures),\n        transformations_applied: Object.keys(featureTransformations),\n        processed_shape: [200, selectedFeatures.size] // Mock data shape\n      };\n      setSelectionResult(result);\n      toast.success('Feature selection applied successfully!');\n    } catch (error) {\n      setError(error.message);\n      toast.error('Feature selection failed: ' + error.message);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const toggleFeature = feature => {\n    setSelectedFeatures(prev => {\n      const newSet = new Set(prev);\n      if (newSet.has(feature)) {\n        newSet.delete(feature);\n      } else {\n        newSet.add(feature);\n      }\n      return newSet;\n    });\n  };\n  const setTransformation = (feature, transformation) => {\n    setFeatureTransformations(prev => {\n      const newTransformations = {\n        ...prev\n      };\n      if (transformation) {\n        newTransformations[feature] = transformation;\n      } else {\n        delete newTransformations[feature];\n      }\n      return newTransformations;\n    });\n  };\n  const selectFeatures = mode => {\n    if (!featureAnalysis) return;\n    const newSelected = new Set();\n    if (mode === 'all') {\n      // Select all features\n      Object.keys(featureAnalysis.feature_summary || {}).forEach(feature => {\n        newSelected.add(feature);\n      });\n    } else if (mode === 'recommended') {\n      // Select recommended features\n      Object.entries(featureAnalysis.feature_summary || {}).forEach(([feature, info]) => {\n        if (info.recommendation === 'include') {\n          newSelected.add(feature);\n        }\n      });\n    }\n    // For 'none', leave the set empty\n\n    setSelectedFeatures(newSelected);\n  };\n  const selectCategoryFeatures = (category, select) => {\n    if (!categories[category]) return;\n    setSelectedFeatures(prev => {\n      const newSet = new Set(prev);\n      categories[category].forEach(feature => {\n        if (select) {\n          newSet.add(feature);\n        } else {\n          newSet.delete(feature);\n        }\n      });\n      return newSet;\n    });\n  };\n  const getQualityClass = score => {\n    if (score >= 80) return 'success';\n    if (score >= 60) return 'info';\n    if (score >= 40) return 'warning';\n    return 'error';\n  };\n  const renderFeatureCard = (feature, info) => {\n    const isSelected = selectedFeatures.has(feature);\n    const qualityClass = getQualityClass(info.data_quality_score);\n    const hasTransformation = featureTransformations[feature];\n    return /*#__PURE__*/_jsxDEV(Card, {\n      variant: \"outlined\",\n      sx: {\n        mb: 2,\n        border: '1px solid',\n        borderColor: isSelected ? 'success.main' : 'divider',\n        backgroundColor: isSelected ? 'success.light' : 'background.paper',\n        opacity: isSelected ? 1 : 0.8,\n        transition: 'all 0.3s ease',\n        '&:hover': {\n          borderColor: isSelected ? 'success.main' : 'primary.main',\n          boxShadow: 1\n        }\n      },\n      children: [/*#__PURE__*/_jsxDEV(CardHeader, {\n        title: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            alignItems: 'center',\n            justifyContent: 'space-between'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle1\",\n            children: feature\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 291,\n            columnNumber: 15\n          }, this), /*#__PURE__*/_jsxDEV(Chip, {\n            label: `${info.data_quality_score}/100`,\n            size: \"small\",\n            color: qualityClass\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 292,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 290,\n          columnNumber: 13\n        }, this),\n        action: /*#__PURE__*/_jsxDEV(Checkbox, {\n          checked: isSelected,\n          onChange: () => toggleFeature(feature),\n          color: \"primary\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 300,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 288,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 308,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardContent, {\n        sx: {\n          pt: 1,\n          pb: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"body2\",\n          color: \"text.secondary\",\n          gutterBottom: true,\n          children: info.description || 'No description available'\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 311,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 1,\n          sx: {\n            mt: 1\n          },\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              children: [\"Type: \", info.feature_type || 'unknown']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 317,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 316,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              children: [\"Missing: \", info.missing_percentage, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              children: [\"Unique: \", info.unique_count]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 327,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 326,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 6,\n            children: info.feature_type === 'numeric' ? /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              children: [\"Outliers: \", info.outlier_percentage || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 333,\n              columnNumber: 17\n            }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"caption\",\n              display: \"block\",\n              children: [\"Most common: \", info.most_common || 'N/A']\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 17\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 331,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 315,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 310,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 345,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(CardActions, {\n        sx: {\n          justifyContent: 'space-between',\n          p: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(FormControl, {\n          size: \"small\",\n          variant: \"outlined\",\n          sx: {\n            minWidth: 120\n          },\n          children: [/*#__PURE__*/_jsxDEV(InputLabel, {\n            id: `transform-label-${feature}`,\n            children: \"Transform\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 349,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Select, {\n            labelId: `transform-label-${feature}`,\n            value: featureTransformations[feature] || '',\n            onChange: e => setTransformation(feature, e.target.value),\n            label: \"Transform\",\n            disabled: !isSelected,\n            size: \"small\",\n            children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n              value: \"\",\n              children: \"None\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 358,\n              columnNumber: 15\n            }, this), info.feature_type === 'numeric' && /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [/*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"standardize\",\n                children: \"Standardize\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 361,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"normalize\",\n                children: \"Normalize\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 362,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"log_transform\",\n                children: \"Log Transform\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 363,\n                columnNumber: 19\n              }, this), /*#__PURE__*/_jsxDEV(MenuItem, {\n                value: \"remove_outliers\",\n                children: \"Remove Outliers\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 364,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 350,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 348,\n          columnNumber: 11\n        }, this), hasTransformation && /*#__PURE__*/_jsxDEV(Chip, {\n          label: hasTransformation.replace('_', ' '),\n          size: \"small\",\n          color: \"primary\",\n          variant: \"outlined\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 371,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 347,\n        columnNumber: 9\n      }, this)]\n    }, feature, true, {\n      fileName: _jsxFileName,\n      lineNumber: 268,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFeatureCategories = () => {\n    if (!featureAnalysis || !categories) return null;\n    return Object.entries(categories).map(([category, features]) => /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 2,\n          mb: 2,\n          bgcolor: 'primary.main',\n          color: 'white'\n        },\n        children: /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            display: 'flex',\n            justifyContent: 'space-between',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              display: 'flex',\n              alignItems: 'center'\n            },\n            children: [/*#__PURE__*/_jsxDEV(Category, {\n              sx: {\n                mr: 1\n              }\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 391,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"subtitle1\",\n              children: [category.toUpperCase(), \" FEATURES (\", features.length, \")\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 392,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Box, {\n            children: [/*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Select All in Category\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => selectCategoryFeatures(category, true),\n                sx: {\n                  color: 'white'\n                },\n                children: /*#__PURE__*/_jsxDEV(SelectAll, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 404,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 399,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 398,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n              title: \"Deselect All in Category\",\n              children: /*#__PURE__*/_jsxDEV(IconButton, {\n                size: \"small\",\n                onClick: () => selectCategoryFeatures(category, false),\n                sx: {\n                  color: 'white'\n                },\n                children: /*#__PURE__*/_jsxDEV(Clear, {}, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 414,\n                  columnNumber: 19\n                }, this)\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 409,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 408,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 397,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 2,\n        children: features.map(feature => /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 12,\n          sm: 6,\n          md: 4,\n          children: featureAnalysis.feature_summary[feature] && renderFeatureCard(feature, featureAnalysis.feature_summary[feature])\n        }, feature, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 13\n        }, this))\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 421,\n        columnNumber: 9\n      }, this)]\n    }, category, true, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this));\n  };\n  const renderFeatureOverview = () => {\n    if (!featureAnalysis) return null;\n    const {\n      data_quality\n    } = featureAnalysis;\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 3,\n        mb: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"h6\",\n        gutterBottom: true,\n        children: \"Feature Overview\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 440,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Grid, {\n        container: true,\n        spacing: 3,\n        children: [/*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (data_quality === null || data_quality === void 0 ? void 0 : data_quality.total_features) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Total Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 445,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: [(data_quality === null || data_quality === void 0 ? void 0 : data_quality.completeness_percentage) || 0, \"%\"]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 458,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Data Completeness\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 461,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 457,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 456,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (data_quality === null || data_quality === void 0 ? void 0 : data_quality.numeric_features) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Numeric Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 467,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          item: true,\n          xs: 6,\n          sm: 3,\n          children: /*#__PURE__*/_jsxDEV(Box, {\n            sx: {\n              textAlign: 'center',\n              p: 2\n            },\n            children: [/*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"h4\",\n              color: \"primary\",\n              children: (data_quality === null || data_quality === void 0 ? void 0 : data_quality.categorical_features) || 0\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 480,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              color: \"text.secondary\",\n              children: \"Categorical Features\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 483,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 479,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 444,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Divider, {\n        sx: {\n          my: 2\n        }\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 490,\n        columnNumber: 9\n      }, this), featureAnalysis.recommendations && featureAnalysis.recommendations.length > 0 && /*#__PURE__*/_jsxDEV(Box, {\n        sx: {\n          mt: 2,\n          p: 2,\n          bgcolor: 'info.light',\n          borderRadius: 1\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          sx: {\n            display: 'flex',\n            alignItems: 'center'\n          },\n          children: [/*#__PURE__*/_jsxDEV(Info, {\n            sx: {\n              mr: 1\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 495,\n            columnNumber: 15\n          }, this), \"Recommendations\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 494,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"ul\", {\n          style: {\n            margin: '8px 0',\n            paddingLeft: '24px'\n          },\n          children: featureAnalysis.recommendations.map((rec, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n            children: /*#__PURE__*/_jsxDEV(Typography, {\n              variant: \"body2\",\n              children: rec\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 502,\n              columnNumber: 19\n            }, this)\n          }, index, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 17\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 499,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 493,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 439,\n      columnNumber: 7\n    }, this);\n  };\n  const renderSelectionSummary = () => {\n    return /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        p: 2,\n        mb: 3,\n        bgcolor: 'primary.light',\n        color: 'primary.contrastText'\n      },\n      children: /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle1\",\n        children: [selectedFeatures.size, \" features selected\", Object.keys(featureTransformations).length > 0 && ` (${Object.keys(featureTransformations).length} with transformations)`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 515,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 514,\n      columnNumber: 7\n    }, this);\n  };\n  const renderSelectionResult = () => {\n    var _selectionResult$sele, _selectionResult$tran, _selectionResult$proc, _selectionResult$proc2;\n    if (!selectionResult) return null;\n    return /*#__PURE__*/_jsxDEV(Alert, {\n      severity: \"success\",\n      sx: {\n        mt: 3\n      },\n      action: /*#__PURE__*/_jsxDEV(Button, {\n        color: \"inherit\",\n        size: \"small\",\n        onClick: () => setSelectionResult(null),\n        children: \"Dismiss\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 532,\n        columnNumber: 11\n      }, this),\n      children: [/*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"subtitle2\",\n        gutterBottom: true,\n        children: \"Feature selection applied successfully!\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 541,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Selected \", ((_selectionResult$sele = selectionResult.selected_features) === null || _selectionResult$sele === void 0 ? void 0 : _selectionResult$sele.length) || 0, \" features\", ((_selectionResult$tran = selectionResult.transformations_applied) === null || _selectionResult$tran === void 0 ? void 0 : _selectionResult$tran.length) > 0 && ` with ${selectionResult.transformations_applied.length} transformations`]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        children: [\"Final data shape: \", ((_selectionResult$proc = selectionResult.processed_shape) === null || _selectionResult$proc === void 0 ? void 0 : _selectionResult$proc[0]) || 0, \" rows \\xD7 \", ((_selectionResult$proc2 = selectionResult.processed_shape) === null || _selectionResult$proc2 === void 0 ? void 0 : _selectionResult$proc2[1]) || 0, \" columns\"]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 549,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 528,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Feature Management\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 558,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      paragraph: true,\n      children: \"Select and transform features for optimal model performance. This step allows you to choose which features to include and apply transformations.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 561,\n      columnNumber: 7\n    }, this), analyzing ? /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        my: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(LinearProgress, {}, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 568,\n        columnNumber: 11\n      }, this), /*#__PURE__*/_jsxDEV(Typography, {\n        variant: \"body2\",\n        sx: {\n          mt: 1,\n          textAlign: 'center'\n        },\n        children: \"Analyzing features...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 569,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 567,\n      columnNumber: 9\n    }, this) : /*#__PURE__*/_jsxDEV(_Fragment, {\n      children: featureAnalysis ? /*#__PURE__*/_jsxDEV(_Fragment, {\n        children: [renderFeatureOverview(), /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            mb: 3\n          },\n          children: [/*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: () => selectFeatures('all'),\n            startIcon: /*#__PURE__*/_jsxDEV(SelectAll, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 584,\n              columnNumber: 30\n            }, this),\n            sx: {\n              mr: 1,\n              mb: 1\n            },\n            children: \"Select All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: () => selectFeatures('none'),\n            startIcon: /*#__PURE__*/_jsxDEV(Clear, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 594,\n              columnNumber: 30\n            }, this),\n            sx: {\n              mr: 1,\n              mb: 1\n            },\n            children: \"Deselect All\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 590,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"contained\",\n            color: \"primary\",\n            onClick: () => selectFeatures('recommended'),\n            startIcon: /*#__PURE__*/_jsxDEV(FilterList, {}, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 604,\n              columnNumber: 30\n            }, this),\n            sx: {\n              mb: 1\n            },\n            children: \"Select Recommended\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 600,\n            columnNumber: 17\n          }, this), /*#__PURE__*/_jsxDEV(Tooltip, {\n            title: \"Features with quality score >= 60 are recommended\",\n            children: /*#__PURE__*/_jsxDEV(IconButton, {\n              sx: {\n                ml: 1,\n                mb: 1\n              },\n              children: /*#__PURE__*/_jsxDEV(Help, {}, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 612,\n                columnNumber: 21\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 611,\n              columnNumber: 19\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 610,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 15\n        }, this), renderSelectionSummary(), renderFeatureCategories(), /*#__PURE__*/_jsxDEV(Button, {\n          variant: \"contained\",\n          color: \"primary\",\n          onClick: applyFeatureSelection,\n          startIcon: /*#__PURE__*/_jsxDEV(CheckCircle, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 625,\n            columnNumber: 28\n          }, this),\n          disabled: selectedFeatures.size === 0,\n          sx: {\n            mb: 3\n          },\n          children: \"Apply Feature Selection\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 621,\n          columnNumber: 15\n        }, this), renderSelectionResult()]\n      }, void 0, true) : /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: analyzeFeatures,\n        startIcon: /*#__PURE__*/_jsxDEV(Tune, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 639,\n          columnNumber: 26\n        }, this),\n        children: \"Analyze Features\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 635,\n        columnNumber: 13\n      }, this)\n    }, void 0, false), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: onBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 22\n        }, this),\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: onNext,\n        endIcon: /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 20\n        }, this),\n        disabled: !selectionResult,\n        children: \"Continue to Model Training\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 557,\n    columnNumber: 5\n  }, this);\n};\n_s(FeatureManagement, \"GPAf4JIfBgj0rRfmhtLqCXckme8=\");\n_c = FeatureManagement;\nexport default FeatureManagement;\nvar _c;\n$RefreshReg$(_c, \"FeatureManagement\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "CardActions", "Divider", "Chip", "FormControl", "InputLabel", "Select", "MenuItem", "Checkbox", "FormControlLabel", "<PERSON><PERSON>", "LinearProgress", "IconButton", "<PERSON><PERSON><PERSON>", "ArrowBack", "ArrowForward", "CheckCircle", "Error", "Warning", "Info", "FilterList", "SelectAll", "Clear", "<PERSON><PERSON>", "Category", "Help", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "FeatureManagement", "onNext", "onBack", "setLoading", "setError", "_s", "featureAnalysis", "setFeatureAnalysis", "selectedFeatures", "setSelectedFeatures", "Set", "featureTransformations", "setFeatureTransformations", "categories", "setCategories", "selectionResult", "setSelectionResult", "analyzing", "setAnalyzing", "analyzeFeatures", "result", "getFeatureAnalysis", "apiError", "analysis", "data_quality", "total_features", "completeness_percentage", "numeric_features", "categorical_features", "feature_summary", "feature_type", "data_quality_score", "missing_percentage", "unique_count", "outlier_percentage", "recommendation", "description", "most_common", "recommendations", "recommended", "Object", "entries", "for<PERSON>ach", "feature", "info", "add", "success", "error", "message", "applyFeatureSelection", "size", "selected_features", "Array", "from", "transformations_applied", "keys", "processed_shape", "toggleFeature", "prev", "newSet", "has", "delete", "setTransformation", "transformation", "newTransformations", "selectFeatures", "mode", "newSelected", "selectCategoryFeatures", "category", "select", "getQualityClass", "score", "renderFeatureCard", "isSelected", "qualityClass", "hasTransformation", "variant", "sx", "mb", "border", "borderColor", "backgroundColor", "opacity", "transition", "boxShadow", "children", "title", "display", "alignItems", "justifyContent", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "label", "color", "action", "checked", "onChange", "pt", "pb", "gutterBottom", "container", "spacing", "mt", "item", "xs", "p", "min<PERSON><PERSON><PERSON>", "id", "labelId", "value", "e", "target", "disabled", "replace", "renderFeatureCategories", "map", "features", "bgcolor", "mr", "toUpperCase", "length", "onClick", "sm", "md", "renderFeatureOverview", "textAlign", "my", "borderRadius", "style", "margin", "paddingLeft", "rec", "index", "renderSelectionSummary", "renderSelectionResult", "_selectionResult$sele", "_selectionResult$tran", "_selectionResult$proc", "_selectionResult$proc2", "severity", "paragraph", "startIcon", "ml", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/src/components/FeatureManagement.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Button,\r\n  Paper,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  CardHeader,\r\n  CardActions,\r\n  Divider,\r\n  Chip,\r\n  FormControl,\r\n  InputLabel,\r\n  Select,\r\n  MenuItem,\r\n  Checkbox,\r\n  FormControlLabel,\r\n  Alert,\r\n  LinearProgress,\r\n  IconButton,\r\n  Tooltip\r\n} from '@mui/material';\r\nimport {\r\n  ArrowBack,\r\n  ArrowForward,\r\n  CheckCircle,\r\n  Error,\r\n  Warning,\r\n  Info,\r\n  FilterList,\r\n  SelectAll,\r\n  Clear,\r\n  Tune,\r\n  Category,\r\n  Help\r\n} from '@mui/icons-material';\r\nimport toast from 'react-hot-toast';\r\n\r\nimport { apiService } from '../services/apiService';\r\n\r\nconst FeatureManagement = ({ onNext, onBack, setLoading, setError }) => {\r\n  const [featureAnalysis, setFeatureAnalysis] = useState(null);\r\n  const [selectedFeatures, setSelectedFeatures] = useState(new Set());\r\n  const [featureTransformations, setFeatureTransformations] = useState({});\r\n  const [categories, setCategories] = useState({});\r\n  const [selectionResult, setSelectionResult] = useState(null);\r\n  const [analyzing, setAnalyzing] = useState(false);\r\n\r\n  useEffect(() => {\r\n    analyzeFeatures();\r\n  }, []);\r\n\r\n  const analyzeFeatures = async () => {\r\n    setAnalyzing(true);\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // Try to get feature analysis from the API\r\n      let result;\r\n      try {\r\n        result = await apiService.getFeatureAnalysis();\r\n      } catch (apiError) {\r\n        // If API endpoint doesn't exist, create mock data\r\n        result = {\r\n          analysis: {\r\n            data_quality: {\r\n              total_features: 8,\r\n              completeness_percentage: 95,\r\n              numeric_features: 4,\r\n              categorical_features: 4\r\n            },\r\n            feature_summary: {\r\n              'product_quantity': {\r\n                feature_type: 'numeric',\r\n                data_quality_score: 85,\r\n                missing_percentage: 0,\r\n                unique_count: 150,\r\n                outlier_percentage: 5,\r\n                recommendation: 'include',\r\n                description: 'Quantity of product being handled'\r\n              },\r\n              'vessel_type': {\r\n                feature_type: 'categorical',\r\n                data_quality_score: 90,\r\n                missing_percentage: 0,\r\n                unique_count: 2,\r\n                most_common: 'Vessel',\r\n                recommendation: 'include',\r\n                description: 'Type of vessel (Vessel or Barge)'\r\n              },\r\n              'operation_type': {\r\n                feature_type: 'categorical',\r\n                data_quality_score: 88,\r\n                missing_percentage: 0,\r\n                unique_count: 2,\r\n                most_common: 'Loading',\r\n                recommendation: 'include',\r\n                description: 'Type of operation (Loading or Discharging)'\r\n              },\r\n              'product_type': {\r\n                feature_type: 'categorical',\r\n                data_quality_score: 75,\r\n                missing_percentage: 2,\r\n                unique_count: 8,\r\n                most_common: 'Gasoline',\r\n                recommendation: 'include',\r\n                description: 'Type of product being handled'\r\n              },\r\n              'hour_of_day': {\r\n                feature_type: 'numeric',\r\n                data_quality_score: 80,\r\n                missing_percentage: 0,\r\n                unique_count: 24,\r\n                outlier_percentage: 0,\r\n                recommendation: 'include',\r\n                description: 'Hour of day when operation starts'\r\n              }\r\n            },\r\n            categories: {\r\n              'operational': ['operation_type', 'hour_of_day'],\r\n              'vessel': ['vessel_type'],\r\n              'product': ['product_type', 'product_quantity']\r\n            },\r\n            recommendations: [\r\n              'All features show good data quality',\r\n              'Consider feature engineering for temporal features',\r\n              'Product quantity may benefit from normalization'\r\n            ]\r\n          }\r\n        };\r\n      }\r\n\r\n      setFeatureAnalysis(result.analysis);\r\n\r\n      // Organize features by category\r\n      if (result.analysis.categories) {\r\n        setCategories(result.analysis.categories);\r\n      }\r\n\r\n      // Initialize selected features with recommended ones\r\n      const recommended = new Set();\r\n      Object.entries(result.analysis.feature_summary || {}).forEach(([feature, info]) => {\r\n        if (info.recommendation === 'include') {\r\n          recommended.add(feature);\r\n        }\r\n      });\r\n      setSelectedFeatures(recommended);\r\n\r\n      toast.success('Feature analysis completed!');\r\n    } catch (error) {\r\n      setError(error.message);\r\n      toast.error('Feature analysis failed: ' + error.message);\r\n    } finally {\r\n      setLoading(false);\r\n      setAnalyzing(false);\r\n    }\r\n  };\r\n\r\n  const applyFeatureSelection = async () => {\r\n    if (selectedFeatures.size === 0) {\r\n      toast.error('Please select at least one feature');\r\n      return;\r\n    }\r\n\r\n    setLoading(true);\r\n    setError(null);\r\n\r\n    try {\r\n      // For now, simulate feature selection since the backend endpoint might not exist\r\n      const result = {\r\n        success: true,\r\n        selected_features: Array.from(selectedFeatures),\r\n        transformations_applied: Object.keys(featureTransformations),\r\n        processed_shape: [200, selectedFeatures.size] // Mock data shape\r\n      };\r\n\r\n      setSelectionResult(result);\r\n      toast.success('Feature selection applied successfully!');\r\n    } catch (error) {\r\n      setError(error.message);\r\n      toast.error('Feature selection failed: ' + error.message);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const toggleFeature = (feature) => {\r\n    setSelectedFeatures(prev => {\r\n      const newSet = new Set(prev);\r\n      if (newSet.has(feature)) {\r\n        newSet.delete(feature);\r\n      } else {\r\n        newSet.add(feature);\r\n      }\r\n      return newSet;\r\n    });\r\n  };\r\n\r\n  const setTransformation = (feature, transformation) => {\r\n    setFeatureTransformations(prev => {\r\n      const newTransformations = { ...prev };\r\n      if (transformation) {\r\n        newTransformations[feature] = transformation;\r\n      } else {\r\n        delete newTransformations[feature];\r\n      }\r\n      return newTransformations;\r\n    });\r\n  };\r\n\r\n  const selectFeatures = (mode) => {\r\n    if (!featureAnalysis) return;\r\n    \r\n    const newSelected = new Set();\r\n    \r\n    if (mode === 'all') {\r\n      // Select all features\r\n      Object.keys(featureAnalysis.feature_summary || {}).forEach(feature => {\r\n        newSelected.add(feature);\r\n      });\r\n    } else if (mode === 'recommended') {\r\n      // Select recommended features\r\n      Object.entries(featureAnalysis.feature_summary || {}).forEach(([feature, info]) => {\r\n        if (info.recommendation === 'include') {\r\n          newSelected.add(feature);\r\n        }\r\n      });\r\n    }\r\n    // For 'none', leave the set empty\r\n    \r\n    setSelectedFeatures(newSelected);\r\n  };\r\n\r\n  const selectCategoryFeatures = (category, select) => {\r\n    if (!categories[category]) return;\r\n    \r\n    setSelectedFeatures(prev => {\r\n      const newSet = new Set(prev);\r\n      \r\n      categories[category].forEach(feature => {\r\n        if (select) {\r\n          newSet.add(feature);\r\n        } else {\r\n          newSet.delete(feature);\r\n        }\r\n      });\r\n      \r\n      return newSet;\r\n    });\r\n  };\r\n\r\n  const getQualityClass = (score) => {\r\n    if (score >= 80) return 'success';\r\n    if (score >= 60) return 'info';\r\n    if (score >= 40) return 'warning';\r\n    return 'error';\r\n  };\r\n\r\n  const renderFeatureCard = (feature, info) => {\r\n    const isSelected = selectedFeatures.has(feature);\r\n    const qualityClass = getQualityClass(info.data_quality_score);\r\n    const hasTransformation = featureTransformations[feature];\r\n    \r\n    return (\r\n      <Card \r\n        key={feature}\r\n        variant=\"outlined\"\r\n        sx={{\r\n          mb: 2,\r\n          border: '1px solid',\r\n          borderColor: isSelected \r\n            ? 'success.main' \r\n            : 'divider',\r\n          backgroundColor: isSelected \r\n            ? 'success.light' \r\n            : 'background.paper',\r\n          opacity: isSelected ? 1 : 0.8,\r\n          transition: 'all 0.3s ease',\r\n          '&:hover': {\r\n            borderColor: isSelected ? 'success.main' : 'primary.main',\r\n            boxShadow: 1\r\n          }\r\n        }}\r\n      >\r\n        <CardHeader\r\n          title={\r\n            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>\r\n              <Typography variant=\"subtitle1\">{feature}</Typography>\r\n              <Chip \r\n                label={`${info.data_quality_score}/100`}\r\n                size=\"small\"\r\n                color={qualityClass}\r\n              />\r\n            </Box>\r\n          }\r\n          action={\r\n            <Checkbox\r\n              checked={isSelected}\r\n              onChange={() => toggleFeature(feature)}\r\n              color=\"primary\"\r\n            />\r\n          }\r\n        />\r\n        \r\n        <Divider />\r\n        \r\n        <CardContent sx={{ pt: 1, pb: 1 }}>\r\n          <Typography variant=\"body2\" color=\"text.secondary\" gutterBottom>\r\n            {info.description || 'No description available'}\r\n          </Typography>\r\n          \r\n          <Grid container spacing={1} sx={{ mt: 1 }}>\r\n            <Grid item xs={6}>\r\n              <Typography variant=\"caption\" display=\"block\">\r\n                Type: {info.feature_type || 'unknown'}\r\n              </Typography>\r\n            </Grid>\r\n            <Grid item xs={6}>\r\n              <Typography variant=\"caption\" display=\"block\">\r\n                Missing: {info.missing_percentage}%\r\n              </Typography>\r\n            </Grid>\r\n            <Grid item xs={6}>\r\n              <Typography variant=\"caption\" display=\"block\">\r\n                Unique: {info.unique_count}\r\n              </Typography>\r\n            </Grid>\r\n            <Grid item xs={6}>\r\n              {info.feature_type === 'numeric' ? (\r\n                <Typography variant=\"caption\" display=\"block\">\r\n                  Outliers: {info.outlier_percentage || 0}%\r\n                </Typography>\r\n              ) : (\r\n                <Typography variant=\"caption\" display=\"block\">\r\n                  Most common: {info.most_common || 'N/A'}\r\n                </Typography>\r\n              )}\r\n            </Grid>\r\n          </Grid>\r\n        </CardContent>\r\n        \r\n        <Divider />\r\n        \r\n        <CardActions sx={{ justifyContent: 'space-between', p: 1 }}>\r\n          <FormControl size=\"small\" variant=\"outlined\" sx={{ minWidth: 120 }}>\r\n            <InputLabel id={`transform-label-${feature}`}>Transform</InputLabel>\r\n            <Select\r\n              labelId={`transform-label-${feature}`}\r\n              value={featureTransformations[feature] || ''}\r\n              onChange={(e) => setTransformation(feature, e.target.value)}\r\n              label=\"Transform\"\r\n              disabled={!isSelected}\r\n              size=\"small\"\r\n            >\r\n              <MenuItem value=\"\">None</MenuItem>\r\n              {info.feature_type === 'numeric' && (\r\n                <>\r\n                  <MenuItem value=\"standardize\">Standardize</MenuItem>\r\n                  <MenuItem value=\"normalize\">Normalize</MenuItem>\r\n                  <MenuItem value=\"log_transform\">Log Transform</MenuItem>\r\n                  <MenuItem value=\"remove_outliers\">Remove Outliers</MenuItem>\r\n                </>\r\n              )}\r\n            </Select>\r\n          </FormControl>\r\n          \r\n          {hasTransformation && (\r\n            <Chip \r\n              label={hasTransformation.replace('_', ' ')}\r\n              size=\"small\"\r\n              color=\"primary\"\r\n              variant=\"outlined\"\r\n            />\r\n          )}\r\n        </CardActions>\r\n      </Card>\r\n    );\r\n  };\r\n\r\n  const renderFeatureCategories = () => {\r\n    if (!featureAnalysis || !categories) return null;\r\n    \r\n    return Object.entries(categories).map(([category, features]) => (\r\n      <Box key={category} sx={{ mb: 4 }}>\r\n        <Paper sx={{ p: 2, mb: 2, bgcolor: 'primary.main', color: 'white' }}>\r\n          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>\r\n            <Box sx={{ display: 'flex', alignItems: 'center' }}>\r\n              <Category sx={{ mr: 1 }} />\r\n              <Typography variant=\"subtitle1\">\r\n                {category.toUpperCase()} FEATURES ({features.length})\r\n              </Typography>\r\n            </Box>\r\n            \r\n            <Box>\r\n              <Tooltip title=\"Select All in Category\">\r\n                <IconButton \r\n                  size=\"small\" \r\n                  onClick={() => selectCategoryFeatures(category, true)}\r\n                  sx={{ color: 'white' }}\r\n                >\r\n                  <SelectAll />\r\n                </IconButton>\r\n              </Tooltip>\r\n              \r\n              <Tooltip title=\"Deselect All in Category\">\r\n                <IconButton\r\n                  size=\"small\"\r\n                  onClick={() => selectCategoryFeatures(category, false)}\r\n                  sx={{ color: 'white' }}\r\n                >\r\n                  <Clear />\r\n                </IconButton>\r\n              </Tooltip>\r\n            </Box>\r\n          </Box>\r\n        </Paper>\r\n        \r\n        <Grid container spacing={2}>\r\n          {features.map(feature => (\r\n            <Grid item xs={12} sm={6} md={4} key={feature}>\r\n              {featureAnalysis.feature_summary[feature] && \r\n                renderFeatureCard(feature, featureAnalysis.feature_summary[feature])}\r\n            </Grid>\r\n          ))}\r\n        </Grid>\r\n      </Box>\r\n    ));\r\n  };\r\n\r\n  const renderFeatureOverview = () => {\r\n    if (!featureAnalysis) return null;\r\n    \r\n    const { data_quality } = featureAnalysis;\r\n    \r\n    return (\r\n      <Paper sx={{ p: 3, mb: 4 }}>\r\n        <Typography variant=\"h6\" gutterBottom>\r\n          Feature Overview\r\n        </Typography>\r\n        \r\n        <Grid container spacing={3}>\r\n          <Grid item xs={6} sm={3}>\r\n            <Box sx={{ textAlign: 'center', p: 2 }}>\r\n              <Typography variant=\"h4\" color=\"primary\">\r\n                {data_quality?.total_features || 0}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                Total Features\r\n              </Typography>\r\n            </Box>\r\n          </Grid>\r\n          \r\n          <Grid item xs={6} sm={3}>\r\n            <Box sx={{ textAlign: 'center', p: 2 }}>\r\n              <Typography variant=\"h4\" color=\"primary\">\r\n                {data_quality?.completeness_percentage || 0}%\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                Data Completeness\r\n              </Typography>\r\n            </Box>\r\n          </Grid>\r\n          \r\n          <Grid item xs={6} sm={3}>\r\n            <Box sx={{ textAlign: 'center', p: 2 }}>\r\n              <Typography variant=\"h4\" color=\"primary\">\r\n                {data_quality?.numeric_features || 0}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                Numeric Features\r\n              </Typography>\r\n            </Box>\r\n          </Grid>\r\n          \r\n          <Grid item xs={6} sm={3}>\r\n            <Box sx={{ textAlign: 'center', p: 2 }}>\r\n              <Typography variant=\"h4\" color=\"primary\">\r\n                {data_quality?.categorical_features || 0}\r\n              </Typography>\r\n              <Typography variant=\"body2\" color=\"text.secondary\">\r\n                Categorical Features\r\n              </Typography>\r\n            </Box>\r\n          </Grid>\r\n        </Grid>\r\n        \r\n        <Divider sx={{ my: 2 }} />\r\n        \r\n        {featureAnalysis.recommendations && featureAnalysis.recommendations.length > 0 && (\r\n          <Box sx={{ mt: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>\r\n            <Typography variant=\"subtitle1\" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>\r\n              <Info sx={{ mr: 1 }} />\r\n              Recommendations\r\n            </Typography>\r\n            \r\n            <ul style={{ margin: '8px 0', paddingLeft: '24px' }}>\r\n              {featureAnalysis.recommendations.map((rec, index) => (\r\n                <li key={index}>\r\n                  <Typography variant=\"body2\">{rec}</Typography>\r\n                </li>\r\n              ))}\r\n            </ul>\r\n          </Box>\r\n        )}\r\n      </Paper>\r\n    );\r\n  };\r\n\r\n  const renderSelectionSummary = () => {\r\n    return (\r\n      <Paper sx={{ p: 2, mb: 3, bgcolor: 'primary.light', color: 'primary.contrastText' }}>\r\n        <Typography variant=\"subtitle1\">\r\n          {selectedFeatures.size} features selected\r\n          {Object.keys(featureTransformations).length > 0 && \r\n            ` (${Object.keys(featureTransformations).length} with transformations)`}\r\n        </Typography>\r\n      </Paper>\r\n    );\r\n  };\r\n\r\n  const renderSelectionResult = () => {\r\n    if (!selectionResult) return null;\r\n    \r\n    return (\r\n      <Alert \r\n        severity=\"success\" \r\n        sx={{ mt: 3 }}\r\n        action={\r\n          <Button \r\n            color=\"inherit\" \r\n            size=\"small\" \r\n            onClick={() => setSelectionResult(null)}\r\n          >\r\n            Dismiss\r\n          </Button>\r\n        }\r\n      >\r\n        <Typography variant=\"subtitle2\" gutterBottom>\r\n          Feature selection applied successfully!\r\n        </Typography>\r\n        <Typography variant=\"body2\">\r\n          Selected {selectionResult.selected_features?.length || 0} features\r\n          {selectionResult.transformations_applied?.length > 0 && \r\n            ` with ${selectionResult.transformations_applied.length} transformations`}\r\n        </Typography>\r\n        <Typography variant=\"body2\">\r\n          Final data shape: {selectionResult.processed_shape?.[0] || 0} rows × {selectionResult.processed_shape?.[1] || 0} columns\r\n        </Typography>\r\n      </Alert>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h4\" gutterBottom>\r\n        Feature Management\r\n      </Typography>\r\n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\r\n        Select and transform features for optimal model performance.\r\n        This step allows you to choose which features to include and apply transformations.\r\n      </Typography>\r\n      \r\n      {analyzing ? (\r\n        <Box sx={{ my: 4 }}>\r\n          <LinearProgress />\r\n          <Typography variant=\"body2\" sx={{ mt: 1, textAlign: 'center' }}>\r\n            Analyzing features...\r\n          </Typography>\r\n        </Box>\r\n      ) : (\r\n        <>\r\n          {featureAnalysis ? (\r\n            <>\r\n              {renderFeatureOverview()}\r\n              \r\n              <Box sx={{ mb: 3 }}>\r\n                <Button \r\n                  variant=\"contained\" \r\n                  color=\"primary\" \r\n                  onClick={() => selectFeatures('all')}\r\n                  startIcon={<SelectAll />}\r\n                  sx={{ mr: 1, mb: 1 }}\r\n                >\r\n                  Select All\r\n                </Button>\r\n                \r\n                <Button\r\n                  variant=\"contained\"\r\n                  color=\"primary\"\r\n                  onClick={() => selectFeatures('none')}\r\n                  startIcon={<Clear />}\r\n                  sx={{ mr: 1, mb: 1 }}\r\n                >\r\n                  Deselect All\r\n                </Button>\r\n                \r\n                <Button \r\n                  variant=\"contained\" \r\n                  color=\"primary\" \r\n                  onClick={() => selectFeatures('recommended')}\r\n                  startIcon={<FilterList />}\r\n                  sx={{ mb: 1 }}\r\n                >\r\n                  Select Recommended\r\n                </Button>\r\n                \r\n                <Tooltip title=\"Features with quality score >= 60 are recommended\">\r\n                  <IconButton sx={{ ml: 1, mb: 1 }}>\r\n                    <Help />\r\n                  </IconButton>\r\n                </Tooltip>\r\n              </Box>\r\n              \r\n              {renderSelectionSummary()}\r\n              \r\n              {renderFeatureCategories()}\r\n              \r\n              <Button \r\n                variant=\"contained\" \r\n                color=\"primary\" \r\n                onClick={applyFeatureSelection}\r\n                startIcon={<CheckCircle />}\r\n                disabled={selectedFeatures.size === 0}\r\n                sx={{ mb: 3 }}\r\n              >\r\n                Apply Feature Selection\r\n              </Button>\r\n              \r\n              {renderSelectionResult()}\r\n            </>\r\n          ) : (\r\n            <Button \r\n              variant=\"contained\" \r\n              color=\"primary\" \r\n              onClick={analyzeFeatures}\r\n              startIcon={<Tune />}\r\n            >\r\n              Analyze Features\r\n            </Button>\r\n          )}\r\n        </>\r\n      )}\r\n      \r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\r\n        <Button \r\n          variant=\"outlined\" \r\n          onClick={onBack}\r\n          startIcon={<ArrowBack />}\r\n        >\r\n          Back\r\n        </Button>\r\n        \r\n        <Button \r\n          variant=\"contained\" \r\n          color=\"primary\" \r\n          onClick={onNext}\r\n          endIcon={<ArrowForward />}\r\n          disabled={!selectionResult}\r\n        >\r\n          Continue to Model Training\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default FeatureManagement;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,WAAW,EACXC,OAAO,EACPC,IAAI,EACJC,WAAW,EACXC,UAAU,EACVC,MAAM,EACNC,QAAQ,EACRC,QAAQ,EACRC,gBAAgB,EAChBC,KAAK,EACLC,cAAc,EACdC,UAAU,EACVC,OAAO,QACF,eAAe;AACtB,SACEC,SAAS,EACTC,YAAY,EACZC,WAAW,EACXC,KAAK,EACLC,OAAO,EACPC,IAAI,EACJC,UAAU,EACVC,SAAS,EACTC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,IAAI,QACC,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,iBAAiB,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EACtE,MAAM,CAACC,eAAe,EAAEC,kBAAkB,CAAC,GAAGhD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAACiD,gBAAgB,EAAEC,mBAAmB,CAAC,GAAGlD,QAAQ,CAAC,IAAImD,GAAG,CAAC,CAAC,CAAC;EACnE,MAAM,CAACC,sBAAsB,EAAEC,yBAAyB,CAAC,GAAGrD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxE,MAAM,CAACsD,UAAU,EAAEC,aAAa,CAAC,GAAGvD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAChD,MAAM,CAACwD,eAAe,EAAEC,kBAAkB,CAAC,GAAGzD,QAAQ,CAAC,IAAI,CAAC;EAC5D,MAAM,CAAC0D,SAAS,EAAEC,YAAY,CAAC,GAAG3D,QAAQ,CAAC,KAAK,CAAC;EAEjDC,SAAS,CAAC,MAAM;IACd2D,eAAe,CAAC,CAAC;EACnB,CAAC,EAAE,EAAE,CAAC;EAEN,MAAMA,eAAe,GAAG,MAAAA,CAAA,KAAY;IAClCD,YAAY,CAAC,IAAI,CAAC;IAClBf,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,IAAIgB,MAAM;MACV,IAAI;QACFA,MAAM,GAAG,MAAMzB,UAAU,CAAC0B,kBAAkB,CAAC,CAAC;MAChD,CAAC,CAAC,OAAOC,QAAQ,EAAE;QACjB;QACAF,MAAM,GAAG;UACPG,QAAQ,EAAE;YACRC,YAAY,EAAE;cACZC,cAAc,EAAE,CAAC;cACjBC,uBAAuB,EAAE,EAAE;cAC3BC,gBAAgB,EAAE,CAAC;cACnBC,oBAAoB,EAAE;YACxB,CAAC;YACDC,eAAe,EAAE;cACf,kBAAkB,EAAE;gBAClBC,YAAY,EAAE,SAAS;gBACvBC,kBAAkB,EAAE,EAAE;gBACtBC,kBAAkB,EAAE,CAAC;gBACrBC,YAAY,EAAE,GAAG;gBACjBC,kBAAkB,EAAE,CAAC;gBACrBC,cAAc,EAAE,SAAS;gBACzBC,WAAW,EAAE;cACf,CAAC;cACD,aAAa,EAAE;gBACbN,YAAY,EAAE,aAAa;gBAC3BC,kBAAkB,EAAE,EAAE;gBACtBC,kBAAkB,EAAE,CAAC;gBACrBC,YAAY,EAAE,CAAC;gBACfI,WAAW,EAAE,QAAQ;gBACrBF,cAAc,EAAE,SAAS;gBACzBC,WAAW,EAAE;cACf,CAAC;cACD,gBAAgB,EAAE;gBAChBN,YAAY,EAAE,aAAa;gBAC3BC,kBAAkB,EAAE,EAAE;gBACtBC,kBAAkB,EAAE,CAAC;gBACrBC,YAAY,EAAE,CAAC;gBACfI,WAAW,EAAE,SAAS;gBACtBF,cAAc,EAAE,SAAS;gBACzBC,WAAW,EAAE;cACf,CAAC;cACD,cAAc,EAAE;gBACdN,YAAY,EAAE,aAAa;gBAC3BC,kBAAkB,EAAE,EAAE;gBACtBC,kBAAkB,EAAE,CAAC;gBACrBC,YAAY,EAAE,CAAC;gBACfI,WAAW,EAAE,UAAU;gBACvBF,cAAc,EAAE,SAAS;gBACzBC,WAAW,EAAE;cACf,CAAC;cACD,aAAa,EAAE;gBACbN,YAAY,EAAE,SAAS;gBACvBC,kBAAkB,EAAE,EAAE;gBACtBC,kBAAkB,EAAE,CAAC;gBACrBC,YAAY,EAAE,EAAE;gBAChBC,kBAAkB,EAAE,CAAC;gBACrBC,cAAc,EAAE,SAAS;gBACzBC,WAAW,EAAE;cACf;YACF,CAAC;YACDvB,UAAU,EAAE;cACV,aAAa,EAAE,CAAC,gBAAgB,EAAE,aAAa,CAAC;cAChD,QAAQ,EAAE,CAAC,aAAa,CAAC;cACzB,SAAS,EAAE,CAAC,cAAc,EAAE,kBAAkB;YAChD,CAAC;YACDyB,eAAe,EAAE,CACf,qCAAqC,EACrC,oDAAoD,EACpD,iDAAiD;UAErD;QACF,CAAC;MACH;MAEA/B,kBAAkB,CAACa,MAAM,CAACG,QAAQ,CAAC;;MAEnC;MACA,IAAIH,MAAM,CAACG,QAAQ,CAACV,UAAU,EAAE;QAC9BC,aAAa,CAACM,MAAM,CAACG,QAAQ,CAACV,UAAU,CAAC;MAC3C;;MAEA;MACA,MAAM0B,WAAW,GAAG,IAAI7B,GAAG,CAAC,CAAC;MAC7B8B,MAAM,CAACC,OAAO,CAACrB,MAAM,CAACG,QAAQ,CAACM,eAAe,IAAI,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,CAAC,CAACC,OAAO,EAAEC,IAAI,CAAC,KAAK;QACjF,IAAIA,IAAI,CAACT,cAAc,KAAK,SAAS,EAAE;UACrCI,WAAW,CAACM,GAAG,CAACF,OAAO,CAAC;QAC1B;MACF,CAAC,CAAC;MACFlC,mBAAmB,CAAC8B,WAAW,CAAC;MAEhC7C,KAAK,CAACoD,OAAO,CAAC,6BAA6B,CAAC;IAC9C,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3C,QAAQ,CAAC2C,KAAK,CAACC,OAAO,CAAC;MACvBtD,KAAK,CAACqD,KAAK,CAAC,2BAA2B,GAAGA,KAAK,CAACC,OAAO,CAAC;IAC1D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;MACjBe,YAAY,CAAC,KAAK,CAAC;IACrB;EACF,CAAC;EAED,MAAM+B,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxC,IAAIzC,gBAAgB,CAAC0C,IAAI,KAAK,CAAC,EAAE;MAC/BxD,KAAK,CAACqD,KAAK,CAAC,oCAAoC,CAAC;MACjD;IACF;IAEA5C,UAAU,CAAC,IAAI,CAAC;IAChBC,QAAQ,CAAC,IAAI,CAAC;IAEd,IAAI;MACF;MACA,MAAMgB,MAAM,GAAG;QACb0B,OAAO,EAAE,IAAI;QACbK,iBAAiB,EAAEC,KAAK,CAACC,IAAI,CAAC7C,gBAAgB,CAAC;QAC/C8C,uBAAuB,EAAEd,MAAM,CAACe,IAAI,CAAC5C,sBAAsB,CAAC;QAC5D6C,eAAe,EAAE,CAAC,GAAG,EAAEhD,gBAAgB,CAAC0C,IAAI,CAAC,CAAC;MAChD,CAAC;MAEDlC,kBAAkB,CAACI,MAAM,CAAC;MAC1B1B,KAAK,CAACoD,OAAO,CAAC,yCAAyC,CAAC;IAC1D,CAAC,CAAC,OAAOC,KAAK,EAAE;MACd3C,QAAQ,CAAC2C,KAAK,CAACC,OAAO,CAAC;MACvBtD,KAAK,CAACqD,KAAK,CAAC,4BAA4B,GAAGA,KAAK,CAACC,OAAO,CAAC;IAC3D,CAAC,SAAS;MACR7C,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsD,aAAa,GAAId,OAAO,IAAK;IACjClC,mBAAmB,CAACiD,IAAI,IAAI;MAC1B,MAAMC,MAAM,GAAG,IAAIjD,GAAG,CAACgD,IAAI,CAAC;MAC5B,IAAIC,MAAM,CAACC,GAAG,CAACjB,OAAO,CAAC,EAAE;QACvBgB,MAAM,CAACE,MAAM,CAAClB,OAAO,CAAC;MACxB,CAAC,MAAM;QACLgB,MAAM,CAACd,GAAG,CAACF,OAAO,CAAC;MACrB;MACA,OAAOgB,MAAM;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMG,iBAAiB,GAAGA,CAACnB,OAAO,EAAEoB,cAAc,KAAK;IACrDnD,yBAAyB,CAAC8C,IAAI,IAAI;MAChC,MAAMM,kBAAkB,GAAG;QAAE,GAAGN;MAAK,CAAC;MACtC,IAAIK,cAAc,EAAE;QAClBC,kBAAkB,CAACrB,OAAO,CAAC,GAAGoB,cAAc;MAC9C,CAAC,MAAM;QACL,OAAOC,kBAAkB,CAACrB,OAAO,CAAC;MACpC;MACA,OAAOqB,kBAAkB;IAC3B,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,cAAc,GAAIC,IAAI,IAAK;IAC/B,IAAI,CAAC5D,eAAe,EAAE;IAEtB,MAAM6D,WAAW,GAAG,IAAIzD,GAAG,CAAC,CAAC;IAE7B,IAAIwD,IAAI,KAAK,KAAK,EAAE;MAClB;MACA1B,MAAM,CAACe,IAAI,CAACjD,eAAe,CAACuB,eAAe,IAAI,CAAC,CAAC,CAAC,CAACa,OAAO,CAACC,OAAO,IAAI;QACpEwB,WAAW,CAACtB,GAAG,CAACF,OAAO,CAAC;MAC1B,CAAC,CAAC;IACJ,CAAC,MAAM,IAAIuB,IAAI,KAAK,aAAa,EAAE;MACjC;MACA1B,MAAM,CAACC,OAAO,CAACnC,eAAe,CAACuB,eAAe,IAAI,CAAC,CAAC,CAAC,CAACa,OAAO,CAAC,CAAC,CAACC,OAAO,EAAEC,IAAI,CAAC,KAAK;QACjF,IAAIA,IAAI,CAACT,cAAc,KAAK,SAAS,EAAE;UACrCgC,WAAW,CAACtB,GAAG,CAACF,OAAO,CAAC;QAC1B;MACF,CAAC,CAAC;IACJ;IACA;;IAEAlC,mBAAmB,CAAC0D,WAAW,CAAC;EAClC,CAAC;EAED,MAAMC,sBAAsB,GAAGA,CAACC,QAAQ,EAAEC,MAAM,KAAK;IACnD,IAAI,CAACzD,UAAU,CAACwD,QAAQ,CAAC,EAAE;IAE3B5D,mBAAmB,CAACiD,IAAI,IAAI;MAC1B,MAAMC,MAAM,GAAG,IAAIjD,GAAG,CAACgD,IAAI,CAAC;MAE5B7C,UAAU,CAACwD,QAAQ,CAAC,CAAC3B,OAAO,CAACC,OAAO,IAAI;QACtC,IAAI2B,MAAM,EAAE;UACVX,MAAM,CAACd,GAAG,CAACF,OAAO,CAAC;QACrB,CAAC,MAAM;UACLgB,MAAM,CAACE,MAAM,CAAClB,OAAO,CAAC;QACxB;MACF,CAAC,CAAC;MAEF,OAAOgB,MAAM;IACf,CAAC,CAAC;EACJ,CAAC;EAED,MAAMY,eAAe,GAAIC,KAAK,IAAK;IACjC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;IACjC,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,MAAM;IAC9B,IAAIA,KAAK,IAAI,EAAE,EAAE,OAAO,SAAS;IACjC,OAAO,OAAO;EAChB,CAAC;EAED,MAAMC,iBAAiB,GAAGA,CAAC9B,OAAO,EAAEC,IAAI,KAAK;IAC3C,MAAM8B,UAAU,GAAGlE,gBAAgB,CAACoD,GAAG,CAACjB,OAAO,CAAC;IAChD,MAAMgC,YAAY,GAAGJ,eAAe,CAAC3B,IAAI,CAACb,kBAAkB,CAAC;IAC7D,MAAM6C,iBAAiB,GAAGjE,sBAAsB,CAACgC,OAAO,CAAC;IAEzD,oBACE9C,OAAA,CAAC/B,IAAI;MAEH+G,OAAO,EAAC,UAAU;MAClBC,EAAE,EAAE;QACFC,EAAE,EAAE,CAAC;QACLC,MAAM,EAAE,WAAW;QACnBC,WAAW,EAAEP,UAAU,GACnB,cAAc,GACd,SAAS;QACbQ,eAAe,EAAER,UAAU,GACvB,eAAe,GACf,kBAAkB;QACtBS,OAAO,EAAET,UAAU,GAAG,CAAC,GAAG,GAAG;QAC7BU,UAAU,EAAE,eAAe;QAC3B,SAAS,EAAE;UACTH,WAAW,EAAEP,UAAU,GAAG,cAAc,GAAG,cAAc;UACzDW,SAAS,EAAE;QACb;MACF,CAAE;MAAAC,QAAA,gBAEFzF,OAAA,CAAC7B,UAAU;QACTuH,KAAK,eACH1F,OAAA,CAACpC,GAAG;UAACqH,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE,QAAQ;YAAEC,cAAc,EAAE;UAAgB,CAAE;UAAAJ,QAAA,gBAClFzF,OAAA,CAACnC,UAAU;YAACmH,OAAO,EAAC,WAAW;YAAAS,QAAA,EAAE3C;UAAO;YAAAgD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAa,CAAC,eACtDjG,OAAA,CAAC1B,IAAI;YACH4H,KAAK,EAAE,GAAGnD,IAAI,CAACb,kBAAkB,MAAO;YACxCmB,IAAI,EAAC,OAAO;YACZ8C,KAAK,EAAErB;UAAa;YAAAgB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACrB,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACC,CACN;QACDG,MAAM,eACJpG,OAAA,CAACrB,QAAQ;UACP0H,OAAO,EAAExB,UAAW;UACpByB,QAAQ,EAAEA,CAAA,KAAM1C,aAAa,CAACd,OAAO,CAAE;UACvCqD,KAAK,EAAC;QAAS;UAAAL,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAChB;MACF;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CAAC,eAEFjG,OAAA,CAAC3B,OAAO;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXjG,OAAA,CAAC9B,WAAW;QAAC+G,EAAE,EAAE;UAAEsB,EAAE,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAf,QAAA,gBAChCzF,OAAA,CAACnC,UAAU;UAACmH,OAAO,EAAC,OAAO;UAACmB,KAAK,EAAC,gBAAgB;UAACM,YAAY;UAAAhB,QAAA,EAC5D1C,IAAI,CAACR,WAAW,IAAI;QAA0B;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACrC,CAAC,eAEbjG,OAAA,CAAChC,IAAI;UAAC0I,SAAS;UAACC,OAAO,EAAE,CAAE;UAAC1B,EAAE,EAAE;YAAE2B,EAAE,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACxCzF,OAAA,CAAChC,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACfzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,SAAS;cAACW,OAAO,EAAC,OAAO;cAAAF,QAAA,GAAC,QACtC,EAAC1C,IAAI,CAACd,YAAY,IAAI,SAAS;YAAA;cAAA6D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPjG,OAAA,CAAChC,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACfzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,SAAS;cAACW,OAAO,EAAC,OAAO;cAAAF,QAAA,GAAC,WACnC,EAAC1C,IAAI,CAACZ,kBAAkB,EAAC,GACpC;YAAA;cAAA2D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPjG,OAAA,CAAChC,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,eACfzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,SAAS;cAACW,OAAO,EAAC,OAAO;cAAAF,QAAA,GAAC,UACpC,EAAC1C,IAAI,CAACX,YAAY;YAAA;cAAA0D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAChB;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACT,CAAC,eACPjG,OAAA,CAAChC,IAAI;YAAC6I,IAAI;YAACC,EAAE,EAAE,CAAE;YAAArB,QAAA,EACd1C,IAAI,CAACd,YAAY,KAAK,SAAS,gBAC9BjC,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,SAAS;cAACW,OAAO,EAAC,OAAO;cAAAF,QAAA,GAAC,YAClC,EAAC1C,IAAI,CAACV,kBAAkB,IAAI,CAAC,EAAC,GAC1C;YAAA;cAAAyD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,gBAEbjG,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,SAAS;cAACW,OAAO,EAAC,OAAO;cAAAF,QAAA,GAAC,eAC/B,EAAC1C,IAAI,CAACP,WAAW,IAAI,KAAK;YAAA;cAAAsD,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7B;UACb;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACG,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI,CAAC,eAEdjG,OAAA,CAAC3B,OAAO;QAAAyH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAEXjG,OAAA,CAAC5B,WAAW;QAAC6G,EAAE,EAAE;UAAEY,cAAc,EAAE,eAAe;UAAEkB,CAAC,EAAE;QAAE,CAAE;QAAAtB,QAAA,gBACzDzF,OAAA,CAACzB,WAAW;UAAC8E,IAAI,EAAC,OAAO;UAAC2B,OAAO,EAAC,UAAU;UAACC,EAAE,EAAE;YAAE+B,QAAQ,EAAE;UAAI,CAAE;UAAAvB,QAAA,gBACjEzF,OAAA,CAACxB,UAAU;YAACyI,EAAE,EAAE,mBAAmBnE,OAAO,EAAG;YAAA2C,QAAA,EAAC;UAAS;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY,CAAC,eACpEjG,OAAA,CAACvB,MAAM;YACLyI,OAAO,EAAE,mBAAmBpE,OAAO,EAAG;YACtCqE,KAAK,EAAErG,sBAAsB,CAACgC,OAAO,CAAC,IAAI,EAAG;YAC7CwD,QAAQ,EAAGc,CAAC,IAAKnD,iBAAiB,CAACnB,OAAO,EAAEsE,CAAC,CAACC,MAAM,CAACF,KAAK,CAAE;YAC5DjB,KAAK,EAAC,WAAW;YACjBoB,QAAQ,EAAE,CAACzC,UAAW;YACtBxB,IAAI,EAAC,OAAO;YAAAoC,QAAA,gBAEZzF,OAAA,CAACtB,QAAQ;cAACyI,KAAK,EAAC,EAAE;cAAA1B,QAAA,EAAC;YAAI;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAU,CAAC,EACjClD,IAAI,CAACd,YAAY,KAAK,SAAS,iBAC9BjC,OAAA,CAAAE,SAAA;cAAAuF,QAAA,gBACEzF,OAAA,CAACtB,QAAQ;gBAACyI,KAAK,EAAC,aAAa;gBAAA1B,QAAA,EAAC;cAAW;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACpDjG,OAAA,CAACtB,QAAQ;gBAACyI,KAAK,EAAC,WAAW;gBAAA1B,QAAA,EAAC;cAAS;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eAChDjG,OAAA,CAACtB,QAAQ;gBAACyI,KAAK,EAAC,eAAe;gBAAA1B,QAAA,EAAC;cAAa;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC,eACxDjG,OAAA,CAACtB,QAAQ;gBAACyI,KAAK,EAAC,iBAAiB;gBAAA1B,QAAA,EAAC;cAAe;gBAAAK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAU,CAAC;YAAA,eAC5D,CACH;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACK,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,EAEblB,iBAAiB,iBAChB/E,OAAA,CAAC1B,IAAI;UACH4H,KAAK,EAAEnB,iBAAiB,CAACwC,OAAO,CAAC,GAAG,EAAE,GAAG,CAAE;UAC3ClE,IAAI,EAAC,OAAO;UACZ8C,KAAK,EAAC,SAAS;UACfnB,OAAO,EAAC;QAAU;UAAAc,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACnB,CACF;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACU,CAAC;IAAA,GA7GTnD,OAAO;MAAAgD,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA8GR,CAAC;EAEX,CAAC;EAED,MAAMuB,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAAC/G,eAAe,IAAI,CAACO,UAAU,EAAE,OAAO,IAAI;IAEhD,OAAO2B,MAAM,CAACC,OAAO,CAAC5B,UAAU,CAAC,CAACyG,GAAG,CAAC,CAAC,CAACjD,QAAQ,EAAEkD,QAAQ,CAAC,kBACzD1H,OAAA,CAACpC,GAAG;MAAgBqH,EAAE,EAAE;QAAEC,EAAE,EAAE;MAAE,CAAE;MAAAO,QAAA,gBAChCzF,OAAA,CAACjC,KAAK;QAACkH,EAAE,EAAE;UAAE8B,CAAC,EAAE,CAAC;UAAE7B,EAAE,EAAE,CAAC;UAAEyC,OAAO,EAAE,cAAc;UAAExB,KAAK,EAAE;QAAQ,CAAE;QAAAV,QAAA,eAClEzF,OAAA,CAACpC,GAAG;UAACqH,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEE,cAAc,EAAE,eAAe;YAAED,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBAClFzF,OAAA,CAACpC,GAAG;YAACqH,EAAE,EAAE;cAAEU,OAAO,EAAE,MAAM;cAAEC,UAAU,EAAE;YAAS,CAAE;YAAAH,QAAA,gBACjDzF,OAAA,CAACL,QAAQ;cAACsF,EAAE,EAAE;gBAAE2C,EAAE,EAAE;cAAE;YAAE;cAAA9B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAC,eAC3BjG,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,WAAW;cAAAS,QAAA,GAC5BjB,QAAQ,CAACqD,WAAW,CAAC,CAAC,EAAC,aAAW,EAACH,QAAQ,CAACI,MAAM,EAAC,GACtD;YAAA;cAAAhC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV,CAAC,eAENjG,OAAA,CAACpC,GAAG;YAAA6H,QAAA,gBACFzF,OAAA,CAAChB,OAAO;cAAC0G,KAAK,EAAC,wBAAwB;cAAAD,QAAA,eACrCzF,OAAA,CAACjB,UAAU;gBACTsE,IAAI,EAAC,OAAO;gBACZ0E,OAAO,EAAEA,CAAA,KAAMxD,sBAAsB,CAACC,QAAQ,EAAE,IAAI,CAAE;gBACtDS,EAAE,EAAE;kBAAEkB,KAAK,EAAE;gBAAQ,CAAE;gBAAAV,QAAA,eAEvBzF,OAAA,CAACR,SAAS;kBAAAsG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC,eAEVjG,OAAA,CAAChB,OAAO;cAAC0G,KAAK,EAAC,0BAA0B;cAAAD,QAAA,eACvCzF,OAAA,CAACjB,UAAU;gBACTsE,IAAI,EAAC,OAAO;gBACZ0E,OAAO,EAAEA,CAAA,KAAMxD,sBAAsB,CAACC,QAAQ,EAAE,KAAK,CAAE;gBACvDS,EAAE,EAAE;kBAAEkB,KAAK,EAAE;gBAAQ,CAAE;gBAAAV,QAAA,eAEvBzF,OAAA,CAACP,KAAK;kBAAAqG,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACC;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACN,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACP,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACD,CAAC,eAERjG,OAAA,CAAChC,IAAI;QAAC0I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAlB,QAAA,EACxBiC,QAAQ,CAACD,GAAG,CAAC3E,OAAO,iBACnB9C,OAAA,CAAChC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,EAAG;UAACkB,EAAE,EAAE,CAAE;UAACC,EAAE,EAAE,CAAE;UAAAxC,QAAA,EAC7BhF,eAAe,CAACuB,eAAe,CAACc,OAAO,CAAC,IACvC8B,iBAAiB,CAAC9B,OAAO,EAAErC,eAAe,CAACuB,eAAe,CAACc,OAAO,CAAC;QAAC,GAFlCA,OAAO;UAAAgD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAGvC,CACP;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC;IAAA,GAzCCzB,QAAQ;MAAAsB,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OA0Cb,CACN,CAAC;EACJ,CAAC;EAED,MAAMiC,qBAAqB,GAAGA,CAAA,KAAM;IAClC,IAAI,CAACzH,eAAe,EAAE,OAAO,IAAI;IAEjC,MAAM;MAAEkB;IAAa,CAAC,GAAGlB,eAAe;IAExC,oBACET,OAAA,CAACjC,KAAK;MAACkH,EAAE,EAAE;QAAE8B,CAAC,EAAE,CAAC;QAAE7B,EAAE,EAAE;MAAE,CAAE;MAAAO,QAAA,gBACzBzF,OAAA,CAACnC,UAAU;QAACmH,OAAO,EAAC,IAAI;QAACyB,YAAY;QAAAhB,QAAA,EAAC;MAEtC;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eAEbjG,OAAA,CAAChC,IAAI;QAAC0I,SAAS;QAACC,OAAO,EAAE,CAAE;QAAAlB,QAAA,gBACzBzF,OAAA,CAAChC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,CAAE;UAACkB,EAAE,EAAE,CAAE;UAAAvC,QAAA,eACtBzF,OAAA,CAACpC,GAAG;YAACqH,EAAE,EAAE;cAAEkD,SAAS,EAAE,QAAQ;cAAEpB,CAAC,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrCzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,IAAI;cAACmB,KAAK,EAAC,SAAS;cAAAV,QAAA,EACrC,CAAA9D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEC,cAAc,KAAI;YAAC;cAAAkE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACxB,CAAC,eACbjG,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,OAAO;cAACmB,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAEnD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjG,OAAA,CAAChC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,CAAE;UAACkB,EAAE,EAAE,CAAE;UAAAvC,QAAA,eACtBzF,OAAA,CAACpC,GAAG;YAACqH,EAAE,EAAE;cAAEkD,SAAS,EAAE,QAAQ;cAAEpB,CAAC,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrCzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,IAAI;cAACmB,KAAK,EAAC,SAAS;cAAAV,QAAA,GACrC,CAAA9D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEE,uBAAuB,KAAI,CAAC,EAAC,GAC9C;YAAA;cAAAiE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC,eACbjG,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,OAAO;cAACmB,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAEnD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjG,OAAA,CAAChC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,CAAE;UAACkB,EAAE,EAAE,CAAE;UAAAvC,QAAA,eACtBzF,OAAA,CAACpC,GAAG;YAACqH,EAAE,EAAE;cAAEkD,SAAS,EAAE,QAAQ;cAAEpB,CAAC,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrCzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,IAAI;cAACmB,KAAK,EAAC,SAAS;cAAAV,QAAA,EACrC,CAAA9D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEG,gBAAgB,KAAI;YAAC;cAAAgE,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC1B,CAAC,eACbjG,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,OAAO;cAACmB,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAEnD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eAEPjG,OAAA,CAAChC,IAAI;UAAC6I,IAAI;UAACC,EAAE,EAAE,CAAE;UAACkB,EAAE,EAAE,CAAE;UAAAvC,QAAA,eACtBzF,OAAA,CAACpC,GAAG;YAACqH,EAAE,EAAE;cAAEkD,SAAS,EAAE,QAAQ;cAAEpB,CAAC,EAAE;YAAE,CAAE;YAAAtB,QAAA,gBACrCzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,IAAI;cAACmB,KAAK,EAAC,SAAS;cAAAV,QAAA,EACrC,CAAA9D,YAAY,aAAZA,YAAY,uBAAZA,YAAY,CAAEI,oBAAoB,KAAI;YAAC;cAAA+D,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC9B,CAAC,eACbjG,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,OAAO;cAACmB,KAAK,EAAC,gBAAgB;cAAAV,QAAA,EAAC;YAEnD;cAAAK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAY,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACV;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,eAEPjG,OAAA,CAAC3B,OAAO;QAAC4G,EAAE,EAAE;UAAEmD,EAAE,EAAE;QAAE;MAAE;QAAAtC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,EAEzBxF,eAAe,CAACgC,eAAe,IAAIhC,eAAe,CAACgC,eAAe,CAACqF,MAAM,GAAG,CAAC,iBAC5E9H,OAAA,CAACpC,GAAG;QAACqH,EAAE,EAAE;UAAE2B,EAAE,EAAE,CAAC;UAAEG,CAAC,EAAE,CAAC;UAAEY,OAAO,EAAE,YAAY;UAAEU,YAAY,EAAE;QAAE,CAAE;QAAA5C,QAAA,gBAC/DzF,OAAA,CAACnC,UAAU;UAACmH,OAAO,EAAC,WAAW;UAACyB,YAAY;UAACxB,EAAE,EAAE;YAAEU,OAAO,EAAE,MAAM;YAAEC,UAAU,EAAE;UAAS,CAAE;UAAAH,QAAA,gBACzFzF,OAAA,CAACV,IAAI;YAAC2F,EAAE,EAAE;cAAE2C,EAAE,EAAE;YAAE;UAAE;YAAA9B,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,mBAEzB;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbjG,OAAA;UAAIsI,KAAK,EAAE;YAAEC,MAAM,EAAE,OAAO;YAAEC,WAAW,EAAE;UAAO,CAAE;UAAA/C,QAAA,EACjDhF,eAAe,CAACgC,eAAe,CAACgF,GAAG,CAAC,CAACgB,GAAG,EAAEC,KAAK,kBAC9C1I,OAAA;YAAAyF,QAAA,eACEzF,OAAA,CAACnC,UAAU;cAACmH,OAAO,EAAC,OAAO;cAAAS,QAAA,EAAEgD;YAAG;cAAA3C,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAa;UAAC,GADvCyC,KAAK;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAEV,CACL;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACA,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF,CACN;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACI,CAAC;EAEZ,CAAC;EAED,MAAM0C,sBAAsB,GAAGA,CAAA,KAAM;IACnC,oBACE3I,OAAA,CAACjC,KAAK;MAACkH,EAAE,EAAE;QAAE8B,CAAC,EAAE,CAAC;QAAE7B,EAAE,EAAE,CAAC;QAAEyC,OAAO,EAAE,eAAe;QAAExB,KAAK,EAAE;MAAuB,CAAE;MAAAV,QAAA,eAClFzF,OAAA,CAACnC,UAAU;QAACmH,OAAO,EAAC,WAAW;QAAAS,QAAA,GAC5B9E,gBAAgB,CAAC0C,IAAI,EAAC,oBACvB,EAACV,MAAM,CAACe,IAAI,CAAC5C,sBAAsB,CAAC,CAACgH,MAAM,GAAG,CAAC,IAC7C,KAAKnF,MAAM,CAACe,IAAI,CAAC5C,sBAAsB,CAAC,CAACgH,MAAM,wBAAwB;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC/D;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEZ,CAAC;EAED,MAAM2C,qBAAqB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,sBAAA;IAClC,IAAI,CAAC9H,eAAe,EAAE,OAAO,IAAI;IAEjC,oBACElB,OAAA,CAACnB,KAAK;MACJoK,QAAQ,EAAC,SAAS;MAClBhE,EAAE,EAAE;QAAE2B,EAAE,EAAE;MAAE,CAAE;MACdR,MAAM,eACJpG,OAAA,CAAClC,MAAM;QACLqI,KAAK,EAAC,SAAS;QACf9C,IAAI,EAAC,OAAO;QACZ0E,OAAO,EAAEA,CAAA,KAAM5G,kBAAkB,CAAC,IAAI,CAAE;QAAAsE,QAAA,EACzC;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CACT;MAAAR,QAAA,gBAEDzF,OAAA,CAACnC,UAAU;QAACmH,OAAO,EAAC,WAAW;QAACyB,YAAY;QAAAhB,QAAA,EAAC;MAE7C;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC,eACbjG,OAAA,CAACnC,UAAU;QAACmH,OAAO,EAAC,OAAO;QAAAS,QAAA,GAAC,WACjB,EAAC,EAAAoD,qBAAA,GAAA3H,eAAe,CAACoC,iBAAiB,cAAAuF,qBAAA,uBAAjCA,qBAAA,CAAmCf,MAAM,KAAI,CAAC,EAAC,WACzD,EAAC,EAAAgB,qBAAA,GAAA5H,eAAe,CAACuC,uBAAuB,cAAAqF,qBAAA,uBAAvCA,qBAAA,CAAyChB,MAAM,IAAG,CAAC,IAClD,SAAS5G,eAAe,CAACuC,uBAAuB,CAACqE,MAAM,kBAAkB;MAAA;QAAAhC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACjE,CAAC,eACbjG,OAAA,CAACnC,UAAU;QAACmH,OAAO,EAAC,OAAO;QAAAS,QAAA,GAAC,oBACR,EAAC,EAAAsD,qBAAA,GAAA7H,eAAe,CAACyC,eAAe,cAAAoF,qBAAA,uBAA/BA,qBAAA,CAAkC,CAAC,CAAC,KAAI,CAAC,EAAC,aAAQ,EAAC,EAAAC,sBAAA,GAAA9H,eAAe,CAACyC,eAAe,cAAAqF,sBAAA,uBAA/BA,sBAAA,CAAkC,CAAC,CAAC,KAAI,CAAC,EAAC,UAClH;MAAA;QAAAlD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACR,CAAC;EAEZ,CAAC;EAED,oBACEjG,OAAA,CAACpC,GAAG;IAAA6H,QAAA,gBACFzF,OAAA,CAACnC,UAAU;MAACmH,OAAO,EAAC,IAAI;MAACyB,YAAY;MAAAhB,QAAA,EAAC;IAEtC;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbjG,OAAA,CAACnC,UAAU;MAACmH,OAAO,EAAC,OAAO;MAACmB,KAAK,EAAC,gBAAgB;MAAC+C,SAAS;MAAAzD,QAAA,EAAC;IAG7D;MAAAK,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,EAEZ7E,SAAS,gBACRpB,OAAA,CAACpC,GAAG;MAACqH,EAAE,EAAE;QAAEmD,EAAE,EAAE;MAAE,CAAE;MAAA3C,QAAA,gBACjBzF,OAAA,CAAClB,cAAc;QAAAgH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAE,CAAC,eAClBjG,OAAA,CAACnC,UAAU;QAACmH,OAAO,EAAC,OAAO;QAACC,EAAE,EAAE;UAAE2B,EAAE,EAAE,CAAC;UAAEuB,SAAS,EAAE;QAAS,CAAE;QAAA1C,QAAA,EAAC;MAEhE;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAY,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACV,CAAC,gBAENjG,OAAA,CAAAE,SAAA;MAAAuF,QAAA,EACGhF,eAAe,gBACdT,OAAA,CAAAE,SAAA;QAAAuF,QAAA,GACGyC,qBAAqB,CAAC,CAAC,eAExBlI,OAAA,CAACpC,GAAG;UAACqH,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAO,QAAA,gBACjBzF,OAAA,CAAClC,MAAM;YACLkH,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACf4B,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,KAAK,CAAE;YACrC+E,SAAS,eAAEnJ,OAAA,CAACR,SAAS;cAAAsG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACzBhB,EAAE,EAAE;cAAE2C,EAAE,EAAE,CAAC;cAAE1C,EAAE,EAAE;YAAE,CAAE;YAAAO,QAAA,EACtB;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjG,OAAA,CAAClC,MAAM;YACLkH,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACf4B,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,MAAM,CAAE;YACtC+E,SAAS,eAAEnJ,OAAA,CAACP,KAAK;cAAAqG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YACrBhB,EAAE,EAAE;cAAE2C,EAAE,EAAE,CAAC;cAAE1C,EAAE,EAAE;YAAE,CAAE;YAAAO,QAAA,EACtB;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjG,OAAA,CAAClC,MAAM;YACLkH,OAAO,EAAC,WAAW;YACnBmB,KAAK,EAAC,SAAS;YACf4B,OAAO,EAAEA,CAAA,KAAM3D,cAAc,CAAC,aAAa,CAAE;YAC7C+E,SAAS,eAAEnJ,OAAA,CAACT,UAAU;cAAAuG,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAE,CAAE;YAC1BhB,EAAE,EAAE;cAAEC,EAAE,EAAE;YAAE,CAAE;YAAAO,QAAA,EACf;UAED;YAAAK,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CAAC,eAETjG,OAAA,CAAChB,OAAO;YAAC0G,KAAK,EAAC,mDAAmD;YAAAD,QAAA,eAChEzF,OAAA,CAACjB,UAAU;cAACkG,EAAE,EAAE;gBAAEmE,EAAE,EAAE,CAAC;gBAAElE,EAAE,EAAE;cAAE,CAAE;cAAAO,QAAA,eAC/BzF,OAAA,CAACJ,IAAI;gBAAAkG,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC,EAEL0C,sBAAsB,CAAC,CAAC,EAExBnB,uBAAuB,CAAC,CAAC,eAE1BxH,OAAA,CAAClC,MAAM;UACLkH,OAAO,EAAC,WAAW;UACnBmB,KAAK,EAAC,SAAS;UACf4B,OAAO,EAAE3E,qBAAsB;UAC/B+F,SAAS,eAAEnJ,OAAA,CAACb,WAAW;YAAA2G,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAC3BqB,QAAQ,EAAE3G,gBAAgB,CAAC0C,IAAI,KAAK,CAAE;UACtC4B,EAAE,EAAE;YAAEC,EAAE,EAAE;UAAE,CAAE;UAAAO,QAAA,EACf;QAED;UAAAK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ,CAAC,EAER2C,qBAAqB,CAAC,CAAC;MAAA,eACxB,CAAC,gBAEH5I,OAAA,CAAClC,MAAM;QACLkH,OAAO,EAAC,WAAW;QACnBmB,KAAK,EAAC,SAAS;QACf4B,OAAO,EAAEzG,eAAgB;QACzB6H,SAAS,eAAEnJ,OAAA,CAACN,IAAI;UAAAoG,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAR,QAAA,EACrB;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ;IACT,gBACD,CACH,eAEDjG,OAAA,CAACpC,GAAG;MAACqH,EAAE,EAAE;QAAEU,OAAO,EAAE,MAAM;QAAEE,cAAc,EAAE,eAAe;QAAEe,EAAE,EAAE;MAAE,CAAE;MAAAnB,QAAA,gBACnEzF,OAAA,CAAClC,MAAM;QACLkH,OAAO,EAAC,UAAU;QAClB+C,OAAO,EAAE1H,MAAO;QAChB8I,SAAS,eAAEnJ,OAAA,CAACf,SAAS;UAAA6G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAR,QAAA,EAC1B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETjG,OAAA,CAAClC,MAAM;QACLkH,OAAO,EAAC,WAAW;QACnBmB,KAAK,EAAC,SAAS;QACf4B,OAAO,EAAE3H,MAAO;QAChBiJ,OAAO,eAAErJ,OAAA,CAACd,YAAY;UAAA4G,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAC1BqB,QAAQ,EAAE,CAACpG,eAAgB;QAAAuE,QAAA,EAC5B;MAED;QAAAK,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAACzF,EAAA,CAjnBIL,iBAAiB;AAAAmJ,EAAA,GAAjBnJ,iBAAiB;AAmnBvB,eAAeA,iBAAiB;AAAC,IAAAmJ,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}