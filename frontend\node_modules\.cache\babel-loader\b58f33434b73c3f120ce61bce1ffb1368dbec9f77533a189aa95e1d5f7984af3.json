{"ast": null, "code": "import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport TransitionGroup from './TransitionGroup';\n/**\n * The `<ReplaceTransition>` component is a specialized `Transition` component\n * that animates between two children.\n *\n * ```jsx\n * <ReplaceTransition in>\n *   <Fade><div>I appear first</div></Fade>\n *   <Fade><div>I replace the above</div></Fade>\n * </ReplaceTransition>\n * ```\n */\n\nvar ReplaceTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ReplaceTransition, _React$Component);\n  function ReplaceTransition() {\n    var _this;\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n    _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n    _this.handleEnter = function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n      return _this.handleLifecycle('onEnter', 0, args);\n    };\n    _this.handleEntering = function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n      return _this.handleLifecycle('onEntering', 0, args);\n    };\n    _this.handleEntered = function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n      return _this.handleLifecycle('onEntered', 0, args);\n    };\n    _this.handleExit = function () {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n      return _this.handleLifecycle('onExit', 1, args);\n    };\n    _this.handleExiting = function () {\n      for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n        args[_key6] = arguments[_key6];\n      }\n      return _this.handleLifecycle('onExiting', 1, args);\n    };\n    _this.handleExited = function () {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n      return _this.handleLifecycle('onExited', 1, args);\n    };\n    return _this;\n  }\n  var _proto = ReplaceTransition.prototype;\n  _proto.handleLifecycle = function handleLifecycle(handler, idx, originalArgs) {\n    var _child$props;\n    var children = this.props.children;\n    var child = React.Children.toArray(children)[idx];\n    if (child.props[handler]) (_child$props = child.props)[handler].apply(_child$props, originalArgs);\n    if (this.props[handler]) {\n      var maybeNode = child.props.nodeRef ? undefined : ReactDOM.findDOMNode(this);\n      this.props[handler](maybeNode);\n    }\n  };\n  _proto.render = function render() {\n    var _this$props = this.props,\n      children = _this$props.children,\n      inProp = _this$props.in,\n      props = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\"]);\n    var _React$Children$toArr = React.Children.toArray(children),\n      first = _React$Children$toArr[0],\n      second = _React$Children$toArr[1];\n    delete props.onEnter;\n    delete props.onEntering;\n    delete props.onEntered;\n    delete props.onExit;\n    delete props.onExiting;\n    delete props.onExited;\n    return /*#__PURE__*/React.createElement(TransitionGroup, props, inProp ? React.cloneElement(first, {\n      key: 'first',\n      onEnter: this.handleEnter,\n      onEntering: this.handleEntering,\n      onEntered: this.handleEntered\n    }) : React.cloneElement(second, {\n      key: 'second',\n      onEnter: this.handleExit,\n      onEntering: this.handleExiting,\n      onEntered: this.handleExited\n    }));\n  };\n  return ReplaceTransition;\n}(React.Component);\nReplaceTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  in: PropTypes.bool.isRequired,\n  children: function children(props, propName) {\n    if (React.Children.count(props[propName]) !== 2) return new Error(\"\\\"\" + propName + \"\\\" must be exactly two transition components.\");\n    return null;\n  }\n} : {};\nexport default ReplaceTransition;", "map": {"version": 3, "names": ["_objectWithoutPropertiesLoose", "_inherits<PERSON><PERSON>e", "PropTypes", "React", "ReactDOM", "TransitionGroup", "ReplaceTransition", "_React$Component", "_this", "_len", "arguments", "length", "_args", "Array", "_key", "call", "apply", "concat", "handleEnter", "_len2", "args", "_key2", "handleLifecycle", "handleEntering", "_len3", "_key3", "handleEntered", "_len4", "_key4", "handleExit", "_len5", "_key5", "handleExiting", "_len6", "_key6", "handleExited", "_len7", "_key7", "_proto", "prototype", "handler", "idx", "originalArgs", "_child$props", "children", "props", "child", "Children", "toArray", "maybeNode", "nodeRef", "undefined", "findDOMNode", "render", "_this$props", "inProp", "in", "_React$Children$toArr", "first", "second", "onEnter", "onEntering", "onEntered", "onExit", "onExiting", "onExited", "createElement", "cloneElement", "key", "Component", "propTypes", "process", "env", "NODE_ENV", "bool", "isRequired", "propName", "count", "Error"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/react-transition-group/esm/ReplaceTransition.js"], "sourcesContent": ["import _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nimport _inherits<PERSON>oose from \"@babel/runtime/helpers/esm/inheritsLoose\";\nimport PropTypes from 'prop-types';\nimport React from 'react';\nimport ReactDOM from 'react-dom';\nimport TransitionGroup from './TransitionGroup';\n/**\n * The `<ReplaceTransition>` component is a specialized `Transition` component\n * that animates between two children.\n *\n * ```jsx\n * <ReplaceTransition in>\n *   <Fade><div>I appear first</div></Fade>\n *   <Fade><div>I replace the above</div></Fade>\n * </ReplaceTransition>\n * ```\n */\n\nvar ReplaceTransition = /*#__PURE__*/function (_React$Component) {\n  _inheritsLoose(ReplaceTransition, _React$Component);\n\n  function ReplaceTransition() {\n    var _this;\n\n    for (var _len = arguments.length, _args = new Array(_len), _key = 0; _key < _len; _key++) {\n      _args[_key] = arguments[_key];\n    }\n\n    _this = _React$Component.call.apply(_React$Component, [this].concat(_args)) || this;\n\n    _this.handleEnter = function () {\n      for (var _len2 = arguments.length, args = new Array(_len2), _key2 = 0; _key2 < _len2; _key2++) {\n        args[_key2] = arguments[_key2];\n      }\n\n      return _this.handleLifecycle('onEnter', 0, args);\n    };\n\n    _this.handleEntering = function () {\n      for (var _len3 = arguments.length, args = new Array(_len3), _key3 = 0; _key3 < _len3; _key3++) {\n        args[_key3] = arguments[_key3];\n      }\n\n      return _this.handleLifecycle('onEntering', 0, args);\n    };\n\n    _this.handleEntered = function () {\n      for (var _len4 = arguments.length, args = new Array(_len4), _key4 = 0; _key4 < _len4; _key4++) {\n        args[_key4] = arguments[_key4];\n      }\n\n      return _this.handleLifecycle('onEntered', 0, args);\n    };\n\n    _this.handleExit = function () {\n      for (var _len5 = arguments.length, args = new Array(_len5), _key5 = 0; _key5 < _len5; _key5++) {\n        args[_key5] = arguments[_key5];\n      }\n\n      return _this.handleLifecycle('onExit', 1, args);\n    };\n\n    _this.handleExiting = function () {\n      for (var _len6 = arguments.length, args = new Array(_len6), _key6 = 0; _key6 < _len6; _key6++) {\n        args[_key6] = arguments[_key6];\n      }\n\n      return _this.handleLifecycle('onExiting', 1, args);\n    };\n\n    _this.handleExited = function () {\n      for (var _len7 = arguments.length, args = new Array(_len7), _key7 = 0; _key7 < _len7; _key7++) {\n        args[_key7] = arguments[_key7];\n      }\n\n      return _this.handleLifecycle('onExited', 1, args);\n    };\n\n    return _this;\n  }\n\n  var _proto = ReplaceTransition.prototype;\n\n  _proto.handleLifecycle = function handleLifecycle(handler, idx, originalArgs) {\n    var _child$props;\n\n    var children = this.props.children;\n    var child = React.Children.toArray(children)[idx];\n    if (child.props[handler]) (_child$props = child.props)[handler].apply(_child$props, originalArgs);\n\n    if (this.props[handler]) {\n      var maybeNode = child.props.nodeRef ? undefined : ReactDOM.findDOMNode(this);\n      this.props[handler](maybeNode);\n    }\n  };\n\n  _proto.render = function render() {\n    var _this$props = this.props,\n        children = _this$props.children,\n        inProp = _this$props.in,\n        props = _objectWithoutPropertiesLoose(_this$props, [\"children\", \"in\"]);\n\n    var _React$Children$toArr = React.Children.toArray(children),\n        first = _React$Children$toArr[0],\n        second = _React$Children$toArr[1];\n\n    delete props.onEnter;\n    delete props.onEntering;\n    delete props.onEntered;\n    delete props.onExit;\n    delete props.onExiting;\n    delete props.onExited;\n    return /*#__PURE__*/React.createElement(TransitionGroup, props, inProp ? React.cloneElement(first, {\n      key: 'first',\n      onEnter: this.handleEnter,\n      onEntering: this.handleEntering,\n      onEntered: this.handleEntered\n    }) : React.cloneElement(second, {\n      key: 'second',\n      onEnter: this.handleExit,\n      onEntering: this.handleExiting,\n      onEntered: this.handleExited\n    }));\n  };\n\n  return ReplaceTransition;\n}(React.Component);\n\nReplaceTransition.propTypes = process.env.NODE_ENV !== \"production\" ? {\n  in: PropTypes.bool.isRequired,\n  children: function children(props, propName) {\n    if (React.Children.count(props[propName]) !== 2) return new Error(\"\\\"\" + propName + \"\\\" must be exactly two transition components.\");\n    return null;\n  }\n} : {};\nexport default ReplaceTransition;"], "mappings": "AAAA,OAAOA,6BAA6B,MAAM,yDAAyD;AACnG,OAAOC,cAAc,MAAM,0CAA0C;AACrE,OAAOC,SAAS,MAAM,YAAY;AAClC,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAOC,QAAQ,MAAM,WAAW;AAChC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,IAAIC,iBAAiB,GAAG,aAAa,UAAUC,gBAAgB,EAAE;EAC/DN,cAAc,CAACK,iBAAiB,EAAEC,gBAAgB,CAAC;EAEnD,SAASD,iBAAiBA,CAAA,EAAG;IAC3B,IAAIE,KAAK;IAET,KAAK,IAAIC,IAAI,GAAGC,SAAS,CAACC,MAAM,EAAEC,KAAK,GAAG,IAAIC,KAAK,CAACJ,IAAI,CAAC,EAAEK,IAAI,GAAG,CAAC,EAAEA,IAAI,GAAGL,IAAI,EAAEK,IAAI,EAAE,EAAE;MACxFF,KAAK,CAACE,IAAI,CAAC,GAAGJ,SAAS,CAACI,IAAI,CAAC;IAC/B;IAEAN,KAAK,GAAGD,gBAAgB,CAACQ,IAAI,CAACC,KAAK,CAACT,gBAAgB,EAAE,CAAC,IAAI,CAAC,CAACU,MAAM,CAACL,KAAK,CAAC,CAAC,IAAI,IAAI;IAEnFJ,KAAK,CAACU,WAAW,GAAG,YAAY;MAC9B,KAAK,IAAIC,KAAK,GAAGT,SAAS,CAACC,MAAM,EAAES,IAAI,GAAG,IAAIP,KAAK,CAACM,KAAK,CAAC,EAAEE,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGF,KAAK,EAAEE,KAAK,EAAE,EAAE;QAC7FD,IAAI,CAACC,KAAK,CAAC,GAAGX,SAAS,CAACW,KAAK,CAAC;MAChC;MAEA,OAAOb,KAAK,CAACc,eAAe,CAAC,SAAS,EAAE,CAAC,EAAEF,IAAI,CAAC;IAClD,CAAC;IAEDZ,KAAK,CAACe,cAAc,GAAG,YAAY;MACjC,KAAK,IAAIC,KAAK,GAAGd,SAAS,CAACC,MAAM,EAAES,IAAI,GAAG,IAAIP,KAAK,CAACW,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FL,IAAI,CAACK,KAAK,CAAC,GAAGf,SAAS,CAACe,KAAK,CAAC;MAChC;MAEA,OAAOjB,KAAK,CAACc,eAAe,CAAC,YAAY,EAAE,CAAC,EAAEF,IAAI,CAAC;IACrD,CAAC;IAEDZ,KAAK,CAACkB,aAAa,GAAG,YAAY;MAChC,KAAK,IAAIC,KAAK,GAAGjB,SAAS,CAACC,MAAM,EAAES,IAAI,GAAG,IAAIP,KAAK,CAACc,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FR,IAAI,CAACQ,KAAK,CAAC,GAAGlB,SAAS,CAACkB,KAAK,CAAC;MAChC;MAEA,OAAOpB,KAAK,CAACc,eAAe,CAAC,WAAW,EAAE,CAAC,EAAEF,IAAI,CAAC;IACpD,CAAC;IAEDZ,KAAK,CAACqB,UAAU,GAAG,YAAY;MAC7B,KAAK,IAAIC,KAAK,GAAGpB,SAAS,CAACC,MAAM,EAAES,IAAI,GAAG,IAAIP,KAAK,CAACiB,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FX,IAAI,CAACW,KAAK,CAAC,GAAGrB,SAAS,CAACqB,KAAK,CAAC;MAChC;MAEA,OAAOvB,KAAK,CAACc,eAAe,CAAC,QAAQ,EAAE,CAAC,EAAEF,IAAI,CAAC;IACjD,CAAC;IAEDZ,KAAK,CAACwB,aAAa,GAAG,YAAY;MAChC,KAAK,IAAIC,KAAK,GAAGvB,SAAS,CAACC,MAAM,EAAES,IAAI,GAAG,IAAIP,KAAK,CAACoB,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7Fd,IAAI,CAACc,KAAK,CAAC,GAAGxB,SAAS,CAACwB,KAAK,CAAC;MAChC;MAEA,OAAO1B,KAAK,CAACc,eAAe,CAAC,WAAW,EAAE,CAAC,EAAEF,IAAI,CAAC;IACpD,CAAC;IAEDZ,KAAK,CAAC2B,YAAY,GAAG,YAAY;MAC/B,KAAK,IAAIC,KAAK,GAAG1B,SAAS,CAACC,MAAM,EAAES,IAAI,GAAG,IAAIP,KAAK,CAACuB,KAAK,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAGD,KAAK,EAAEC,KAAK,EAAE,EAAE;QAC7FjB,IAAI,CAACiB,KAAK,CAAC,GAAG3B,SAAS,CAAC2B,KAAK,CAAC;MAChC;MAEA,OAAO7B,KAAK,CAACc,eAAe,CAAC,UAAU,EAAE,CAAC,EAAEF,IAAI,CAAC;IACnD,CAAC;IAED,OAAOZ,KAAK;EACd;EAEA,IAAI8B,MAAM,GAAGhC,iBAAiB,CAACiC,SAAS;EAExCD,MAAM,CAAChB,eAAe,GAAG,SAASA,eAAeA,CAACkB,OAAO,EAAEC,GAAG,EAAEC,YAAY,EAAE;IAC5E,IAAIC,YAAY;IAEhB,IAAIC,QAAQ,GAAG,IAAI,CAACC,KAAK,CAACD,QAAQ;IAClC,IAAIE,KAAK,GAAG3C,KAAK,CAAC4C,QAAQ,CAACC,OAAO,CAACJ,QAAQ,CAAC,CAACH,GAAG,CAAC;IACjD,IAAIK,KAAK,CAACD,KAAK,CAACL,OAAO,CAAC,EAAE,CAACG,YAAY,GAAGG,KAAK,CAACD,KAAK,EAAEL,OAAO,CAAC,CAACxB,KAAK,CAAC2B,YAAY,EAAED,YAAY,CAAC;IAEjG,IAAI,IAAI,CAACG,KAAK,CAACL,OAAO,CAAC,EAAE;MACvB,IAAIS,SAAS,GAAGH,KAAK,CAACD,KAAK,CAACK,OAAO,GAAGC,SAAS,GAAG/C,QAAQ,CAACgD,WAAW,CAAC,IAAI,CAAC;MAC5E,IAAI,CAACP,KAAK,CAACL,OAAO,CAAC,CAACS,SAAS,CAAC;IAChC;EACF,CAAC;EAEDX,MAAM,CAACe,MAAM,GAAG,SAASA,MAAMA,CAAA,EAAG;IAChC,IAAIC,WAAW,GAAG,IAAI,CAACT,KAAK;MACxBD,QAAQ,GAAGU,WAAW,CAACV,QAAQ;MAC/BW,MAAM,GAAGD,WAAW,CAACE,EAAE;MACvBX,KAAK,GAAG7C,6BAA6B,CAACsD,WAAW,EAAE,CAAC,UAAU,EAAE,IAAI,CAAC,CAAC;IAE1E,IAAIG,qBAAqB,GAAGtD,KAAK,CAAC4C,QAAQ,CAACC,OAAO,CAACJ,QAAQ,CAAC;MACxDc,KAAK,GAAGD,qBAAqB,CAAC,CAAC,CAAC;MAChCE,MAAM,GAAGF,qBAAqB,CAAC,CAAC,CAAC;IAErC,OAAOZ,KAAK,CAACe,OAAO;IACpB,OAAOf,KAAK,CAACgB,UAAU;IACvB,OAAOhB,KAAK,CAACiB,SAAS;IACtB,OAAOjB,KAAK,CAACkB,MAAM;IACnB,OAAOlB,KAAK,CAACmB,SAAS;IACtB,OAAOnB,KAAK,CAACoB,QAAQ;IACrB,OAAO,aAAa9D,KAAK,CAAC+D,aAAa,CAAC7D,eAAe,EAAEwC,KAAK,EAAEU,MAAM,GAAGpD,KAAK,CAACgE,YAAY,CAACT,KAAK,EAAE;MACjGU,GAAG,EAAE,OAAO;MACZR,OAAO,EAAE,IAAI,CAAC1C,WAAW;MACzB2C,UAAU,EAAE,IAAI,CAACtC,cAAc;MAC/BuC,SAAS,EAAE,IAAI,CAACpC;IAClB,CAAC,CAAC,GAAGvB,KAAK,CAACgE,YAAY,CAACR,MAAM,EAAE;MAC9BS,GAAG,EAAE,QAAQ;MACbR,OAAO,EAAE,IAAI,CAAC/B,UAAU;MACxBgC,UAAU,EAAE,IAAI,CAAC7B,aAAa;MAC9B8B,SAAS,EAAE,IAAI,CAAC3B;IAClB,CAAC,CAAC,CAAC;EACL,CAAC;EAED,OAAO7B,iBAAiB;AAC1B,CAAC,CAACH,KAAK,CAACkE,SAAS,CAAC;AAElB/D,iBAAiB,CAACgE,SAAS,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,GAAG;EACpEjB,EAAE,EAAEtD,SAAS,CAACwE,IAAI,CAACC,UAAU;EAC7B/B,QAAQ,EAAE,SAASA,QAAQA,CAACC,KAAK,EAAE+B,QAAQ,EAAE;IAC3C,IAAIzE,KAAK,CAAC4C,QAAQ,CAAC8B,KAAK,CAAChC,KAAK,CAAC+B,QAAQ,CAAC,CAAC,KAAK,CAAC,EAAE,OAAO,IAAIE,KAAK,CAAC,IAAI,GAAGF,QAAQ,GAAG,+CAA+C,CAAC;IACpI,OAAO,IAAI;EACb;AACF,CAAC,GAAG,CAAC,CAAC;AACN,eAAetE,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}