{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Pumptimemodel\\\\frontend\\\\src\\\\components\\\\ModelEvaluation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Divider, Chip, Alert, List, ListItem, ListItemIcon, ListItemText, Tab, Tabs } from '@mui/material';\nimport { ArrowBack, ArrowForward, BarChart, Timeline, BubbleChart, TrendingUp, Info, Warning, CheckCircle, Error } from '@mui/icons-material';\nimport toast from 'react-hot-toast';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModelEvaluation = ({\n  onNext,\n  onBack,\n  bestModel,\n  setLoading,\n  setError\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [modelPerformance, setModelPerformance] = useState(null);\n  const [featureImportance, setFeatureImportance] = useState(null);\n  const [visualizations, setVisualizations] = useState({});\n  const predictionScatterRef = useRef(null);\n  const residualsRef = useRef(null);\n  useEffect(() => {\n    fetchModelPerformance();\n    fetchFeatureImportance();\n  }, [bestModel]);\n\n  // Effect to render Plotly charts when visualization data changes\n  useEffect(() => {\n    const renderPlotlyChart = async (ref, chartData, chartId) => {\n      if (ref.current && chartData && chartData.type === 'plotly') {\n        try {\n          const Plotly = await import('plotly.js-dist');\n          const parsedChart = JSON.parse(chartData.chart);\n          await Plotly.newPlot(ref.current, parsedChart.data, parsedChart.layout, {\n            responsive: true\n          });\n        } catch (error) {\n          console.error(`Failed to render ${chartId}:`, error);\n        }\n      }\n    };\n    if (visualizations.predictionScatter) {\n      renderPlotlyChart(predictionScatterRef, visualizations.predictionScatter, 'prediction-scatter');\n    }\n    if (visualizations.residuals) {\n      renderPlotlyChart(residualsRef, visualizations.residuals, 'residuals');\n    }\n  }, [visualizations]);\n  const fetchModelPerformance = async () => {\n    setLoading(true);\n    try {\n      const result = await apiService.getModelPerformance();\n      setModelPerformance(result);\n    } catch (error) {\n      setError('Failed to fetch model performance: ' + error.message);\n      toast.error('Failed to fetch model performance');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFeatureImportance = async () => {\n    setLoading(true);\n    try {\n      const result = await apiService.getFeatureImportance();\n      setFeatureImportance(result);\n    } catch (error) {\n      setError('Failed to fetch feature importance: ' + error.message);\n      toast.error('Failed to fetch feature importance');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTabChange = (_, newValue) => {\n    setActiveTab(newValue);\n\n    // Load visualization data for the selected tab if not already loaded\n    if (newValue === 1 && !visualizations.predictionScatter) {\n      loadVisualization('predictionScatter');\n    } else if (newValue === 2 && !visualizations.featureImportance) {\n      loadVisualization('featureImportance');\n    } else if (newValue === 3 && !visualizations.residuals) {\n      loadVisualization('residuals');\n    }\n  };\n  const loadVisualization = async vizType => {\n    setLoading(true);\n    try {\n      let result;\n      switch (vizType) {\n        case 'predictionScatter':\n          result = await apiService.generateVisualization('prediction_scatter');\n          // Handle both image and Plotly JSON responses\n          if (result.image) {\n            result = result.image;\n          } else if (result.chart) {\n            result = {\n              type: 'plotly',\n              chart: result.chart\n            };\n          }\n          break;\n        case 'featureImportance':\n          result = await apiService.generateVisualization('feature_importance');\n          break;\n        case 'residuals':\n          result = await apiService.generateVisualization('residuals_plot');\n          // Handle both image and Plotly JSON responses\n          if (result.image) {\n            result = result.image;\n          } else if (result.chart) {\n            result = {\n              type: 'plotly',\n              chart: result.chart\n            };\n          }\n          break;\n        default:\n          return;\n      }\n      setVisualizations(prev => ({\n        ...prev,\n        [vizType]: result\n      }));\n    } catch (error) {\n      console.error(`Failed to load ${vizType} visualization:`, error);\n      setError(`Failed to load ${vizType} visualization: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderPerformanceMetrics = () => {\n    var _modelPerformance$das, _bestModelMetrics$r, _bestModelMetrics$rms, _bestModelMetrics$mae, _bestModelMetrics$map, _bestModelMetrics$r2, _bestModelMetrics$r3, _bestModelMetrics$r4, _bestModelMetrics$mae2, _bestModelMetrics$map2;\n    if (!modelPerformance) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Loading model performance metrics...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 157,\n        columnNumber: 9\n      }, this);\n    }\n    const metrics = (_modelPerformance$das = modelPerformance.dashboard) !== null && _modelPerformance$das !== void 0 && _modelPerformance$das.rmse_comparison ? JSON.parse(modelPerformance.dashboard.rmse_comparison) : null;\n    if (!metrics) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No performance metrics available for the model.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 168,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Helper function to get color based on R² score\n    const getR2Color = r2 => {\n      if (r2 >= 0.9) return 'success';\n      if (r2 >= 0.7) return 'primary';\n      if (r2 >= 0.5) return 'warning';\n      return 'error';\n    };\n\n    // Helper function to get interpretation of R² score\n    const getR2Interpretation = r2 => {\n      if (r2 >= 0.9) return 'Excellent fit';\n      if (r2 >= 0.7) return 'Good fit';\n      if (r2 >= 0.5) return 'Moderate fit';\n      if (r2 >= 0.0) return 'Poor fit';\n      return 'Very poor fit';\n    };\n\n    // Extract metrics for the best model\n    const bestModelMetrics = modelPerformance.best_model_metrics || {\n      r2: 0.7,\n      rmse: 45,\n      mae: 35,\n      mape: 15\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(BarChart, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 203,\n            columnNumber: 13\n          }, this), \"Model Performance Summary\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 202,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$r = bestModelMetrics.r2) === null || _bestModelMetrics$r === void 0 ? void 0 : _bestModelMetrics$r.toFixed(3)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"R\\xB2 Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: getR2Interpretation(bestModelMetrics.r2 || 0),\n                  size: \"small\",\n                  color: getR2Color(bestModelMetrics.r2 || 0),\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 217,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 210,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 209,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 208,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$rms = bestModelMetrics.rmse) === null || _bestModelMetrics$rms === void 0 ? void 0 : _bestModelMetrics$rms.toFixed(2)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"RMSE (minutes)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Root Mean Squared Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 236,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 229,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 228,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 227,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$mae = bestModelMetrics.mae) === null || _bestModelMetrics$mae === void 0 ? void 0 : _bestModelMetrics$mae.toFixed(2)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"MAE (minutes)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Mean Absolute Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 255,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 248,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 247,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 246,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: [((_bestModelMetrics$map = bestModelMetrics.mape) === null || _bestModelMetrics$map === void 0 ? void 0 : _bestModelMetrics$map.toFixed(1)) || 'N/A', \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 268,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"MAPE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 271,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Mean Absolute Percentage Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 274,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 267,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 266,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 265,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 207,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 285,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Model Evaluation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 287,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: getR2Color(bestModelMetrics.r2 || 0),\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: [bestModel, \" - \", getR2Interpretation(bestModelMetrics.r2 || 0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 295,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: bestModelMetrics.r2 >= 0.7 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows good predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r2 = bestModelMetrics.r2) === null || _bestModelMetrics$r2 === void 0 ? void 0 : _bestModelMetrics$r2.toFixed(3), \"indicates that \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model.\"]\n            }, void 0, true) : bestModelMetrics.r2 >= 0.5 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows moderate predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r3 = bestModelMetrics.r2) === null || _bestModelMetrics$r3 === void 0 ? void 0 : _bestModelMetrics$r3.toFixed(3), \"indicates that only \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model. Consider adding more relevant features or collecting more data.\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows poor predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r4 = bestModelMetrics.r2) === null || _bestModelMetrics$r4 === void 0 ? void 0 : _bestModelMetrics$r4.toFixed(3), \"indicates that only \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model. Consider:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Adding more training data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 318,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Engineering additional features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 319,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Checking data quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 320,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Trying different algorithms\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 321,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 317,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 299,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 291,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Practical Interpretation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 328,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 335,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 334,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `Average Error: ±${((_bestModelMetrics$mae2 = bestModelMetrics.mae) === null || _bestModelMetrics$mae2 === void 0 ? void 0 : _bestModelMetrics$mae2.toFixed(1)) || 'N/A'} minutes`,\n              secondary: \"On average, predictions will be off by this amount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 337,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 333,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 345,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 344,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `Percentage Error: ${((_bestModelMetrics$map2 = bestModelMetrics.mape) === null || _bestModelMetrics$map2 === void 0 ? void 0 : _bestModelMetrics$map2.toFixed(1)) || 'N/A'}%`,\n              secondary: \"Relative error as a percentage of the actual value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 347,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 343,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 332,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 201,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 200,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFeatureImportance = () => {\n    if (!featureImportance) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Loading feature importance data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 361,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Parse feature importance data\n    const chartData = featureImportance.chart ? JSON.parse(featureImportance.chart) : null;\n    if (!chartData) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No feature importance data available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 373,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Extract top features from Plotly chart data\n    let topFeatures = [];\n    if (chartData.data && chartData.data.length > 0) {\n      const plotlyData = chartData.data[0];\n\n      // Handle horizontal bar chart format (x=importance, y=feature names)\n      if (plotlyData.x && plotlyData.y) {\n        topFeatures = plotlyData.y.map((featureName, index) => ({\n          name: featureName,\n          importance: plotlyData.x[index]\n        }));\n      }\n    }\n\n    // Fallback: try to extract from other possible data structures\n    if (topFeatures.length === 0 && chartData.data) {\n      topFeatures = chartData.data;\n    }\n\n    // If still no features, show a message\n    if (topFeatures.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No feature importance data could be extracted from the model.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 402,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(BubbleChart, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 412,\n            columnNumber: 13\n          }, this), \"Feature Importance\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 411,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Feature importance shows which variables have the most influence on predictions. Higher values indicate stronger influence on the model's output.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 417,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 416,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: topFeatures.slice(0, 10).map((feature, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                color: index < 3 ? 'primary' : 'action'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 427,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 426,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `${index + 1}. ${feature.name || feature.x || `Feature ${index + 1}`}`,\n              secondary: `Importance: ${(() => {\n                const importance = feature.importance || feature.y;\n                return typeof importance === 'number' ? importance.toFixed(3) : 'N/A';\n              })()}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 429,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '50%',\n                ml: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 12,\n                  bgcolor: index < 3 ? 'primary.main' : 'primary.light',\n                  width: `${Math.min(100, Math.max(5, (typeof (feature.importance || feature.y) === 'number' ? feature.importance || feature.y : 0) * 100))}%`,\n                  minWidth: '5%',\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 437,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 436,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 425,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 423,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 451,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Insights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 453,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 460,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 459,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Top Predictors\",\n              secondary: `The top 3 features account for a significant portion of the model's predictive power.`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 462,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 458,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 470,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 469,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Feature Selection\",\n              secondary: \"Consider focusing on the top features for simplified models or data collection.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 472,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 468,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 457,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 410,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 409,\n      columnNumber: 7\n    }, this);\n  };\n  const renderPredictionScatter = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(Timeline, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 488,\n            columnNumber: 13\n          }, this), \"Predicted vs Actual Values\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 487,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"This chart shows how well the model's predictions match the actual values. Points closer to the diagonal line indicate more accurate predictions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 493,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 492,\n          columnNumber: 11\n        }, this), visualizations.predictionScatter ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 2\n          },\n          children: typeof visualizations.predictionScatter === 'string' ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/png;base64,${visualizations.predictionScatter}`,\n            alt: \"Prediction Scatter Plot\",\n            style: {\n              maxWidth: '100%',\n              height: 'auto'\n            },\n            onError: e => {\n              console.error('Failed to load prediction scatter image');\n              e.target.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 502,\n            columnNumber: 17\n          }, this) : visualizations.predictionScatter.type === 'plotly' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: predictionScatterRef,\n            style: {\n              width: '100%',\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 512,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"error\",\n            children: \"Invalid visualization format\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 517,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 500,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            children: activeTab === 1 ? 'Loading visualization...' : 'Click to load visualization'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 522,\n            columnNumber: 15\n          }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => loadVisualization('predictionScatter'),\n            sx: {\n              mt: 2\n            },\n            children: \"Load Prediction Scatter Plot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 526,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 521,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 486,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 485,\n      columnNumber: 7\n    }, this);\n  };\n  const renderResidualsPlot = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(Timeline, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 546,\n            columnNumber: 13\n          }, this), \"Residuals Analysis\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 545,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Residuals are the differences between predicted and actual values. Ideally, residuals should be randomly distributed around zero with no pattern.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 551,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 550,\n          columnNumber: 11\n        }, this), visualizations.residuals ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 2\n          },\n          children: typeof visualizations.residuals === 'string' ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/png;base64,${visualizations.residuals}`,\n            alt: \"Residuals Plot\",\n            style: {\n              maxWidth: '100%',\n              height: 'auto'\n            },\n            onError: e => {\n              console.error('Failed to load residuals image');\n              e.target.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 560,\n            columnNumber: 17\n          }, this) : visualizations.residuals.type === 'plotly' ? /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: residualsRef,\n            style: {\n              width: '100%',\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"error\",\n            children: \"Invalid visualization format\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 575,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 558,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            children: activeTab === 3 ? 'Loading visualization...' : 'Click to load visualization'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 580,\n            columnNumber: 15\n          }, this), activeTab === 3 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => loadVisualization('residuals'),\n            sx: {\n              mt: 2\n            },\n            children: \"Load Residuals Plot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 584,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 579,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 595,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Interpretation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 597,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Random Distribution\",\n              secondary: \"Residuals should be randomly scattered around zero with no clear pattern.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Warning, {\n                color: \"warning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 614,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 613,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Patterns to Watch For\",\n              secondary: \"Trends, curves, or clusters in residuals indicate the model may be missing important patterns.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 616,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 612,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 601,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 544,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 543,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Model Evaluation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 629,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      paragraph: true,\n      children: \"Evaluate model performance and understand feature importance. This step helps you interpret the model's predictions and identify areas for improvement.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 632,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        variant: \"fullWidth\",\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BarChart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 645,\n            columnNumber: 22\n          }, this),\n          label: \"Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 645,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(Timeline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 646,\n            columnNumber: 22\n          }, this),\n          label: \"Predictions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 646,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BubbleChart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 647,\n            columnNumber: 22\n          }, this),\n          label: \"Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 647,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 648,\n            columnNumber: 22\n          }, this),\n          label: \"Residuals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 648,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 638,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 637,\n      columnNumber: 7\n    }, this), activeTab === 0 && renderPerformanceMetrics(), activeTab === 1 && renderPredictionScatter(), activeTab === 2 && renderFeatureImportance(), activeTab === 3 && renderResidualsPlot(), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: onBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 661,\n          columnNumber: 22\n        }, this),\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 658,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: onNext,\n        endIcon: /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 670,\n          columnNumber: 20\n        }, this),\n        children: \"Continue to Predictions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 666,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 657,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 628,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelEvaluation, \"sWlfV6A9CAXbe3AU7kdgDWKM/sQ=\");\n_c = ModelEvaluation;\nexport default ModelEvaluation;\nvar _c;\n$RefreshReg$(_c, \"ModelEvaluation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "useRef", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "Chip", "<PERSON><PERSON>", "List", "ListItem", "ListItemIcon", "ListItemText", "Tab", "Tabs", "ArrowBack", "ArrowForward", "<PERSON><PERSON><PERSON>", "Timeline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrendingUp", "Info", "Warning", "CheckCircle", "Error", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModelEvaluation", "onNext", "onBack", "bestModel", "setLoading", "setError", "_s", "activeTab", "setActiveTab", "modelPerformance", "setModelPerformance", "featureImportance", "setFeatureImportance", "visualizations", "setVisualizations", "predictionScatterRef", "residualsRef", "fetchModelPerformance", "fetchFeatureImportance", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ref", "chartData", "chartId", "current", "type", "<PERSON><PERSON><PERSON>", "parsed<PERSON><PERSON>", "JSON", "parse", "chart", "newPlot", "data", "layout", "responsive", "error", "console", "predictionScatter", "residuals", "result", "getModelPerformance", "message", "getFeatureImportance", "handleTabChange", "_", "newValue", "loadVisualization", "vizType", "generateVisualization", "image", "prev", "renderPerformanceMetrics", "_modelPerformance$das", "_bestModelMetrics$r", "_bestModelMetrics$rms", "_bestModelMetrics$mae", "_bestModelMetrics$map", "_bestModelMetrics$r2", "_bestModelMetrics$r3", "_bestModelMetrics$r4", "_bestModelMetrics$mae2", "_bestModelMetrics$map2", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "metrics", "dashboard", "rmse_comparison", "getR2Color", "r2", "getR2Interpretation", "bestModelMetrics", "best_model_metrics", "rmse", "mae", "mape", "sx", "p", "mb", "variant", "gutterBottom", "mr", "verticalAlign", "container", "spacing", "item", "xs", "sm", "md", "color", "toFixed", "label", "size", "mt", "my", "primary", "secondary", "renderFeatureImportance", "topFeatures", "length", "plotlyData", "x", "y", "map", "featureName", "index", "name", "importance", "slice", "feature", "width", "ml", "height", "bgcolor", "Math", "min", "max", "min<PERSON><PERSON><PERSON>", "borderRadius", "renderPredictionScatter", "textAlign", "src", "alt", "style", "max<PERSON><PERSON><PERSON>", "onError", "e", "target", "display", "onClick", "renderResidualsPlot", "paragraph", "value", "onChange", "indicatorColor", "textColor", "icon", "justifyContent", "startIcon", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/src/components/ModelEvaluation.js"], "sourcesContent": ["import React, { useState, useEffect, useRef } from 'react';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Button,\r\n  Paper,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Divider,\r\n  Chip,\r\n  Alert,\r\n  List,\r\n  ListItem,\r\n  ListItemIcon,\r\n  ListItemText,\r\n  Tab,\r\n  Tabs\r\n} from '@mui/material';\r\nimport {\r\n  ArrowBack,\r\n  ArrowForward,\r\n  BarChart,\r\n  Timeline,\r\n  BubbleChart,\r\n  TrendingUp,\r\n  Info,\r\n  Warning,\r\n  CheckCircle,\r\n  Error\r\n} from '@mui/icons-material';\r\nimport toast from 'react-hot-toast';\r\n\r\nimport { apiService } from '../services/apiService';\r\n\r\nconst ModelEvaluation = ({ onNext, onBack, bestModel, setLoading, setError }) => {\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [modelPerformance, setModelPerformance] = useState(null);\r\n  const [featureImportance, setFeatureImportance] = useState(null);\r\n  const [visualizations, setVisualizations] = useState({});\r\n  const predictionScatterRef = useRef(null);\r\n  const residualsRef = useRef(null);\r\n\r\n  useEffect(() => {\r\n    fetchModelPerformance();\r\n    fetchFeatureImportance();\r\n  }, [bestModel]);\r\n\r\n  // Effect to render Plotly charts when visualization data changes\r\n  useEffect(() => {\r\n    const renderPlotlyChart = async (ref, chartData, chartId) => {\r\n      if (ref.current && chartData && chartData.type === 'plotly') {\r\n        try {\r\n          const Plotly = await import('plotly.js-dist');\r\n          const parsedChart = JSON.parse(chartData.chart);\r\n          await Plotly.newPlot(ref.current, parsedChart.data, parsedChart.layout, {responsive: true});\r\n        } catch (error) {\r\n          console.error(`Failed to render ${chartId}:`, error);\r\n        }\r\n      }\r\n    };\r\n\r\n    if (visualizations.predictionScatter) {\r\n      renderPlotlyChart(predictionScatterRef, visualizations.predictionScatter, 'prediction-scatter');\r\n    }\r\n\r\n    if (visualizations.residuals) {\r\n      renderPlotlyChart(residualsRef, visualizations.residuals, 'residuals');\r\n    }\r\n  }, [visualizations]);\r\n\r\n  const fetchModelPerformance = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const result = await apiService.getModelPerformance();\r\n      setModelPerformance(result);\r\n    } catch (error) {\r\n      setError('Failed to fetch model performance: ' + error.message);\r\n      toast.error('Failed to fetch model performance');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchFeatureImportance = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const result = await apiService.getFeatureImportance();\r\n      setFeatureImportance(result);\r\n    } catch (error) {\r\n      setError('Failed to fetch feature importance: ' + error.message);\r\n      toast.error('Failed to fetch feature importance');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (_, newValue) => {\r\n    setActiveTab(newValue);\r\n    \r\n    // Load visualization data for the selected tab if not already loaded\r\n    if (newValue === 1 && !visualizations.predictionScatter) {\r\n      loadVisualization('predictionScatter');\r\n    } else if (newValue === 2 && !visualizations.featureImportance) {\r\n      loadVisualization('featureImportance');\r\n    } else if (newValue === 3 && !visualizations.residuals) {\r\n      loadVisualization('residuals');\r\n    }\r\n  };\r\n\r\n  const loadVisualization = async (vizType) => {\r\n    setLoading(true);\r\n    try {\r\n      let result;\r\n\r\n      switch (vizType) {\r\n        case 'predictionScatter':\r\n          result = await apiService.generateVisualization('prediction_scatter');\r\n          // Handle both image and Plotly JSON responses\r\n          if (result.image) {\r\n            result = result.image;\r\n          } else if (result.chart) {\r\n            result = { type: 'plotly', chart: result.chart };\r\n          }\r\n          break;\r\n        case 'featureImportance':\r\n          result = await apiService.generateVisualization('feature_importance');\r\n          break;\r\n        case 'residuals':\r\n          result = await apiService.generateVisualization('residuals_plot');\r\n          // Handle both image and Plotly JSON responses\r\n          if (result.image) {\r\n            result = result.image;\r\n          } else if (result.chart) {\r\n            result = { type: 'plotly', chart: result.chart };\r\n          }\r\n          break;\r\n        default:\r\n          return;\r\n      }\r\n\r\n      setVisualizations(prev => ({\r\n        ...prev,\r\n        [vizType]: result\r\n      }));\r\n    } catch (error) {\r\n      console.error(`Failed to load ${vizType} visualization:`, error);\r\n      setError(`Failed to load ${vizType} visualization: ${error.message}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const renderPerformanceMetrics = () => {\r\n    if (!modelPerformance) {\r\n      return (\r\n        <Alert severity=\"info\">\r\n          Loading model performance metrics...\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    const metrics = modelPerformance.dashboard?.rmse_comparison ? \r\n      JSON.parse(modelPerformance.dashboard.rmse_comparison) : null;\r\n    \r\n    if (!metrics) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No performance metrics available for the model.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Helper function to get color based on R² score\r\n    const getR2Color = (r2) => {\r\n      if (r2 >= 0.9) return 'success';\r\n      if (r2 >= 0.7) return 'primary';\r\n      if (r2 >= 0.5) return 'warning';\r\n      return 'error';\r\n    };\r\n    \r\n    // Helper function to get interpretation of R² score\r\n    const getR2Interpretation = (r2) => {\r\n      if (r2 >= 0.9) return 'Excellent fit';\r\n      if (r2 >= 0.7) return 'Good fit';\r\n      if (r2 >= 0.5) return 'Moderate fit';\r\n      if (r2 >= 0.0) return 'Poor fit';\r\n      return 'Very poor fit';\r\n    };\r\n\r\n    // Extract metrics for the best model\r\n    const bestModelMetrics = modelPerformance.best_model_metrics || {\r\n      r2: 0.7,\r\n      rmse: 45,\r\n      mae: 35,\r\n      mape: 15\r\n    };\r\n\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <BarChart sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Model Performance Summary\r\n          </Typography>\r\n          \r\n          <Grid container spacing={3}>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.r2?.toFixed(3) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    R² Score\r\n                  </Typography>\r\n                  <Chip \r\n                    label={getR2Interpretation(bestModelMetrics.r2 || 0)}\r\n                    size=\"small\"\r\n                    color={getR2Color(bestModelMetrics.r2 || 0)}\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.rmse?.toFixed(2) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    RMSE (minutes)\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Root Mean Squared Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.mae?.toFixed(2) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    MAE (minutes)\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Mean Absolute Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.mape?.toFixed(1) || 'N/A'}%\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    MAPE\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Mean Absolute Percentage Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n          </Grid>\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Model Evaluation\r\n          </Typography>\r\n          \r\n          <Alert \r\n            severity={getR2Color(bestModelMetrics.r2 || 0)}\r\n            sx={{ mb: 2 }}\r\n          >\r\n            <Typography variant=\"subtitle2\" gutterBottom>\r\n              {bestModel} - {getR2Interpretation(bestModelMetrics.r2 || 0)}\r\n            </Typography>\r\n            \r\n            <Typography variant=\"body2\">\r\n              {bestModelMetrics.r2 >= 0.7 ? (\r\n                <>\r\n                  This model shows good predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model.\r\n                </>\r\n              ) : bestModelMetrics.r2 >= 0.5 ? (\r\n                <>\r\n                  This model shows moderate predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model. Consider adding more relevant features or collecting more data.\r\n                </>\r\n              ) : (\r\n                <>\r\n                  This model shows poor predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model. Consider:\r\n                  <ul>\r\n                    <li>Adding more training data</li>\r\n                    <li>Engineering additional features</li>\r\n                    <li>Checking data quality</li>\r\n                    <li>Trying different algorithms</li>\r\n                  </ul>\r\n                </>\r\n              )}\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Practical Interpretation\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary={`Average Error: ±${bestModelMetrics.mae?.toFixed(1) || 'N/A'} minutes`}\r\n                secondary=\"On average, predictions will be off by this amount\"\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary={`Percentage Error: ${bestModelMetrics.mape?.toFixed(1) || 'N/A'}%`}\r\n                secondary=\"Relative error as a percentage of the actual value\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderFeatureImportance = () => {\r\n    if (!featureImportance) {\r\n      return (\r\n        <Alert severity=\"info\">\r\n          Loading feature importance data...\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Parse feature importance data\r\n    const chartData = featureImportance.chart ?\r\n      JSON.parse(featureImportance.chart) : null;\r\n\r\n    if (!chartData) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No feature importance data available.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Extract top features from Plotly chart data\r\n    let topFeatures = [];\r\n\r\n    if (chartData.data && chartData.data.length > 0) {\r\n      const plotlyData = chartData.data[0];\r\n\r\n      // Handle horizontal bar chart format (x=importance, y=feature names)\r\n      if (plotlyData.x && plotlyData.y) {\r\n        topFeatures = plotlyData.y.map((featureName, index) => ({\r\n          name: featureName,\r\n          importance: plotlyData.x[index]\r\n        }));\r\n      }\r\n    }\r\n\r\n    // Fallback: try to extract from other possible data structures\r\n    if (topFeatures.length === 0 && chartData.data) {\r\n      topFeatures = chartData.data;\r\n    }\r\n\r\n    // If still no features, show a message\r\n    if (topFeatures.length === 0) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No feature importance data could be extracted from the model.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <BubbleChart sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Feature Importance\r\n          </Typography>\r\n\r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              Feature importance shows which variables have the most influence on predictions.\r\n              Higher values indicate stronger influence on the model's output.\r\n            </Typography>\r\n          </Alert>\r\n\r\n          <List>\r\n            {topFeatures.slice(0, 10).map((feature, index) => (\r\n              <ListItem key={index}>\r\n                <ListItemIcon>\r\n                  <TrendingUp color={index < 3 ? 'primary' : 'action'} />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={`${index + 1}. ${feature.name || feature.x || `Feature ${index + 1}`}`}\r\n                  secondary={`Importance: ${(() => {\r\n                    const importance = feature.importance || feature.y;\r\n                    return typeof importance === 'number' ? importance.toFixed(3) : 'N/A';\r\n                  })()}`}\r\n                />\r\n                <Box sx={{ width: '50%', ml: 2 }}>\r\n                  <Box\r\n                    sx={{\r\n                      height: 12,\r\n                      bgcolor: index < 3 ? 'primary.main' : 'primary.light',\r\n                      width: `${Math.min(100, Math.max(5, (typeof (feature.importance || feature.y) === 'number' ? (feature.importance || feature.y) : 0) * 100))}%`,\r\n                      minWidth: '5%',\r\n                      borderRadius: 1\r\n                    }}\r\n                  />\r\n                </Box>\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Insights\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Top Predictors\"\r\n                secondary={`The top 3 features account for a significant portion of the model's predictive power.`}\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Feature Selection\"\r\n                secondary=\"Consider focusing on the top features for simplified models or data collection.\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderPredictionScatter = () => {\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Predicted vs Actual Values\r\n          </Typography>\r\n          \r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              This chart shows how well the model's predictions match the actual values.\r\n              Points closer to the diagonal line indicate more accurate predictions.\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          {visualizations.predictionScatter ? (\r\n            <Box sx={{ textAlign: 'center', mt: 2 }}>\r\n              {typeof visualizations.predictionScatter === 'string' ? (\r\n                <img\r\n                  src={`data:image/png;base64,${visualizations.predictionScatter}`}\r\n                  alt=\"Prediction Scatter Plot\"\r\n                  style={{ maxWidth: '100%', height: 'auto' }}\r\n                  onError={(e) => {\r\n                    console.error('Failed to load prediction scatter image');\r\n                    e.target.style.display = 'none';\r\n                  }}\r\n                />\r\n              ) : visualizations.predictionScatter.type === 'plotly' ? (\r\n                <div\r\n                  ref={predictionScatterRef}\r\n                  style={{ width: '100%', height: '400px' }}\r\n                />\r\n              ) : (\r\n                <Typography color=\"error\">Invalid visualization format</Typography>\r\n              )}\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ textAlign: 'center', p: 4 }}>\r\n              <Typography color=\"text.secondary\">\r\n                {activeTab === 1 ? 'Loading visualization...' : 'Click to load visualization'}\r\n              </Typography>\r\n              {activeTab === 1 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => loadVisualization('predictionScatter')}\r\n                  sx={{ mt: 2 }}\r\n                >\r\n                  Load Prediction Scatter Plot\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderResidualsPlot = () => {\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Residuals Analysis\r\n          </Typography>\r\n          \r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              Residuals are the differences between predicted and actual values.\r\n              Ideally, residuals should be randomly distributed around zero with no pattern.\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          {visualizations.residuals ? (\r\n            <Box sx={{ textAlign: 'center', mt: 2 }}>\r\n              {typeof visualizations.residuals === 'string' ? (\r\n                <img\r\n                  src={`data:image/png;base64,${visualizations.residuals}`}\r\n                  alt=\"Residuals Plot\"\r\n                  style={{ maxWidth: '100%', height: 'auto' }}\r\n                  onError={(e) => {\r\n                    console.error('Failed to load residuals image');\r\n                    e.target.style.display = 'none';\r\n                  }}\r\n                />\r\n              ) : visualizations.residuals.type === 'plotly' ? (\r\n                <div\r\n                  ref={residualsRef}\r\n                  style={{ width: '100%', height: '400px' }}\r\n                />\r\n              ) : (\r\n                <Typography color=\"error\">Invalid visualization format</Typography>\r\n              )}\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ textAlign: 'center', p: 4 }}>\r\n              <Typography color=\"text.secondary\">\r\n                {activeTab === 3 ? 'Loading visualization...' : 'Click to load visualization'}\r\n              </Typography>\r\n              {activeTab === 3 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => loadVisualization('residuals')}\r\n                  sx={{ mt: 2 }}\r\n                >\r\n                  Load Residuals Plot\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          )}\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Interpretation\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Random Distribution\"\r\n                secondary=\"Residuals should be randomly scattered around zero with no clear pattern.\"\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Warning color=\"warning\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Patterns to Watch For\"\r\n                secondary=\"Trends, curves, or clusters in residuals indicate the model may be missing important patterns.\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h4\" gutterBottom>\r\n        Model Evaluation\r\n      </Typography>\r\n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\r\n        Evaluate model performance and understand feature importance.\r\n        This step helps you interpret the model's predictions and identify areas for improvement.\r\n      </Typography>\r\n      \r\n      <Paper sx={{ mb: 3 }}>\r\n        <Tabs\r\n          value={activeTab}\r\n          onChange={handleTabChange}\r\n          variant=\"fullWidth\"\r\n          indicatorColor=\"primary\"\r\n          textColor=\"primary\"\r\n        >\r\n          <Tab icon={<BarChart />} label=\"Performance\" />\r\n          <Tab icon={<Timeline />} label=\"Predictions\" />\r\n          <Tab icon={<BubbleChart />} label=\"Features\" />\r\n          <Tab icon={<TrendingUp />} label=\"Residuals\" />\r\n        </Tabs>\r\n      </Paper>\r\n      \r\n      {activeTab === 0 && renderPerformanceMetrics()}\r\n      {activeTab === 1 && renderPredictionScatter()}\r\n      {activeTab === 2 && renderFeatureImportance()}\r\n      {activeTab === 3 && renderResidualsPlot()}\r\n      \r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\r\n        <Button \r\n          variant=\"outlined\" \r\n          onClick={onBack}\r\n          startIcon={<ArrowBack />}\r\n        >\r\n          Back\r\n        </Button>\r\n        \r\n        <Button \r\n          variant=\"contained\" \r\n          color=\"primary\" \r\n          onClick={onNext}\r\n          endIcon={<ArrowForward />}\r\n        >\r\n          Continue to Predictions\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ModelEvaluation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,EAAEC,MAAM,QAAQ,OAAO;AAC1D,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SACEC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,KAAK,QACA,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC,SAAS;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC;EACxD,MAAMkD,oBAAoB,GAAGhD,MAAM,CAAC,IAAI,CAAC;EACzC,MAAMiD,YAAY,GAAGjD,MAAM,CAAC,IAAI,CAAC;EAEjCD,SAAS,CAAC,MAAM;IACdmD,qBAAqB,CAAC,CAAC;IACvBC,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACf,SAAS,CAAC,CAAC;;EAEf;EACArC,SAAS,CAAC,MAAM;IACd,MAAMqD,iBAAiB,GAAG,MAAAA,CAAOC,GAAG,EAAEC,SAAS,EAAEC,OAAO,KAAK;MAC3D,IAAIF,GAAG,CAACG,OAAO,IAAIF,SAAS,IAAIA,SAAS,CAACG,IAAI,KAAK,QAAQ,EAAE;QAC3D,IAAI;UACF,MAAMC,MAAM,GAAG,MAAM,MAAM,CAAC,gBAAgB,CAAC;UAC7C,MAAMC,WAAW,GAAGC,IAAI,CAACC,KAAK,CAACP,SAAS,CAACQ,KAAK,CAAC;UAC/C,MAAMJ,MAAM,CAACK,OAAO,CAACV,GAAG,CAACG,OAAO,EAAEG,WAAW,CAACK,IAAI,EAAEL,WAAW,CAACM,MAAM,EAAE;YAACC,UAAU,EAAE;UAAI,CAAC,CAAC;QAC7F,CAAC,CAAC,OAAOC,KAAK,EAAE;UACdC,OAAO,CAACD,KAAK,CAAC,oBAAoBZ,OAAO,GAAG,EAAEY,KAAK,CAAC;QACtD;MACF;IACF,CAAC;IAED,IAAIrB,cAAc,CAACuB,iBAAiB,EAAE;MACpCjB,iBAAiB,CAACJ,oBAAoB,EAAEF,cAAc,CAACuB,iBAAiB,EAAE,oBAAoB,CAAC;IACjG;IAEA,IAAIvB,cAAc,CAACwB,SAAS,EAAE;MAC5BlB,iBAAiB,CAACH,YAAY,EAAEH,cAAc,CAACwB,SAAS,EAAE,WAAW,CAAC;IACxE;EACF,CAAC,EAAE,CAACxB,cAAc,CAAC,CAAC;EAEpB,MAAMI,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCb,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkC,MAAM,GAAG,MAAM3C,UAAU,CAAC4C,mBAAmB,CAAC,CAAC;MACrD7B,mBAAmB,CAAC4B,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd7B,QAAQ,CAAC,qCAAqC,GAAG6B,KAAK,CAACM,OAAO,CAAC;MAC/D9C,KAAK,CAACwC,KAAK,CAAC,mCAAmC,CAAC;IAClD,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMc,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzCd,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMkC,MAAM,GAAG,MAAM3C,UAAU,CAAC8C,oBAAoB,CAAC,CAAC;MACtD7B,oBAAoB,CAAC0B,MAAM,CAAC;IAC9B,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACd7B,QAAQ,CAAC,sCAAsC,GAAG6B,KAAK,CAACM,OAAO,CAAC;MAChE9C,KAAK,CAACwC,KAAK,CAAC,oCAAoC,CAAC;IACnD,CAAC,SAAS;MACR9B,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMsC,eAAe,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACvCpC,YAAY,CAACoC,QAAQ,CAAC;;IAEtB;IACA,IAAIA,QAAQ,KAAK,CAAC,IAAI,CAAC/B,cAAc,CAACuB,iBAAiB,EAAE;MACvDS,iBAAiB,CAAC,mBAAmB,CAAC;IACxC,CAAC,MAAM,IAAID,QAAQ,KAAK,CAAC,IAAI,CAAC/B,cAAc,CAACF,iBAAiB,EAAE;MAC9DkC,iBAAiB,CAAC,mBAAmB,CAAC;IACxC,CAAC,MAAM,IAAID,QAAQ,KAAK,CAAC,IAAI,CAAC/B,cAAc,CAACwB,SAAS,EAAE;MACtDQ,iBAAiB,CAAC,WAAW,CAAC;IAChC;EACF,CAAC;EAED,MAAMA,iBAAiB,GAAG,MAAOC,OAAO,IAAK;IAC3C1C,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIkC,MAAM;MAEV,QAAQQ,OAAO;QACb,KAAK,mBAAmB;UACtBR,MAAM,GAAG,MAAM3C,UAAU,CAACoD,qBAAqB,CAAC,oBAAoB,CAAC;UACrE;UACA,IAAIT,MAAM,CAACU,KAAK,EAAE;YAChBV,MAAM,GAAGA,MAAM,CAACU,KAAK;UACvB,CAAC,MAAM,IAAIV,MAAM,CAACT,KAAK,EAAE;YACvBS,MAAM,GAAG;cAAEd,IAAI,EAAE,QAAQ;cAAEK,KAAK,EAAES,MAAM,CAACT;YAAM,CAAC;UAClD;UACA;QACF,KAAK,mBAAmB;UACtBS,MAAM,GAAG,MAAM3C,UAAU,CAACoD,qBAAqB,CAAC,oBAAoB,CAAC;UACrE;QACF,KAAK,WAAW;UACdT,MAAM,GAAG,MAAM3C,UAAU,CAACoD,qBAAqB,CAAC,gBAAgB,CAAC;UACjE;UACA,IAAIT,MAAM,CAACU,KAAK,EAAE;YAChBV,MAAM,GAAGA,MAAM,CAACU,KAAK;UACvB,CAAC,MAAM,IAAIV,MAAM,CAACT,KAAK,EAAE;YACvBS,MAAM,GAAG;cAAEd,IAAI,EAAE,QAAQ;cAAEK,KAAK,EAAES,MAAM,CAACT;YAAM,CAAC;UAClD;UACA;QACF;UACE;MACJ;MAEAf,iBAAiB,CAACmC,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAACH,OAAO,GAAGR;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOJ,KAAK,EAAE;MACdC,OAAO,CAACD,KAAK,CAAC,kBAAkBY,OAAO,iBAAiB,EAAEZ,KAAK,CAAC;MAChE7B,QAAQ,CAAC,kBAAkByC,OAAO,mBAAmBZ,KAAK,CAACM,OAAO,EAAE,CAAC;IACvE,CAAC,SAAS;MACRpC,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM8C,wBAAwB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACrC,IAAI,CAACnD,gBAAgB,EAAE;MACrB,oBACEZ,OAAA,CAACpB,KAAK;QAACoF,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,MAAMC,OAAO,GAAG,CAAAhB,qBAAA,GAAA1C,gBAAgB,CAAC2D,SAAS,cAAAjB,qBAAA,eAA1BA,qBAAA,CAA4BkB,eAAe,GACzD1C,IAAI,CAACC,KAAK,CAACnB,gBAAgB,CAAC2D,SAAS,CAACC,eAAe,CAAC,GAAG,IAAI;IAE/D,IAAI,CAACF,OAAO,EAAE;MACZ,oBACEtE,OAAA,CAACpB,KAAK;QAACoF,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,MAAMI,UAAU,GAAIC,EAAE,IAAK;MACzB,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,OAAO,OAAO;IAChB,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAID,EAAE,IAAK;MAClC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,eAAe;MACrC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,UAAU;MAChC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,cAAc;MACpC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,UAAU;MAChC,OAAO,eAAe;IACxB,CAAC;;IAED;IACA,MAAME,gBAAgB,GAAGhE,gBAAgB,CAACiE,kBAAkB,IAAI;MAC9DH,EAAE,EAAE,GAAG;MACPI,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IAED,oBACEhF,OAAA,CAAC7B,GAAG;MAAA8F,QAAA,eACFjE,OAAA,CAAC1B,KAAK;QAAC2G,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBACzBjE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,IAAI;UAACC,YAAY;UAAApB,QAAA,gBACnCjE,OAAA,CAACX,QAAQ;YAAC4F,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACzB,IAAI;UAACiH,SAAS;UAACC,OAAO,EAAE,CAAE;UAAAxB,QAAA,gBACzBjE,OAAA,CAACzB,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eAC9BjE,OAAA,CAACxB,IAAI;cAAC4G,OAAO,EAAC,UAAU;cAAAnB,QAAA,eACtBjE,OAAA,CAACvB,WAAW;gBAAAwF,QAAA,gBACVjE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAApB,QAAA,EAClD,EAAAV,mBAAA,GAAAqB,gBAAgB,CAACF,EAAE,cAAAnB,mBAAA,uBAAnBA,mBAAA,CAAqBwC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACbrE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA7B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrE,OAAA,CAACrB,IAAI;kBACHqH,KAAK,EAAErB,mBAAmB,CAACC,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;kBACrDuB,IAAI,EAAC,OAAO;kBACZH,KAAK,EAAErB,UAAU,CAACG,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;kBAC5CO,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPrE,OAAA,CAACzB,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eAC9BjE,OAAA,CAACxB,IAAI;cAAC4G,OAAO,EAAC,UAAU;cAAAnB,QAAA,eACtBjE,OAAA,CAACvB,WAAW;gBAAAwF,QAAA,gBACVjE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAApB,QAAA,EAClD,EAAAT,qBAAA,GAAAoB,gBAAgB,CAACE,IAAI,cAAAtB,qBAAA,uBAArBA,qBAAA,CAAuBuC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACbrE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA7B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrE,OAAA,CAACrB,IAAI;kBACHqH,KAAK,EAAC,yBAAyB;kBAC/BC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPrE,OAAA,CAACzB,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eAC9BjE,OAAA,CAACxB,IAAI;cAAC4G,OAAO,EAAC,UAAU;cAAAnB,QAAA,eACtBjE,OAAA,CAACvB,WAAW;gBAAAwF,QAAA,gBACVjE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAApB,QAAA,EAClD,EAAAR,qBAAA,GAAAmB,gBAAgB,CAACG,GAAG,cAAAtB,qBAAA,uBAApBA,qBAAA,CAAsBsC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACbrE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA7B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrE,OAAA,CAACrB,IAAI;kBACHqH,KAAK,EAAC,qBAAqB;kBAC3BC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPrE,OAAA,CAACzB,IAAI;YAACmH,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA5B,QAAA,eAC9BjE,OAAA,CAACxB,IAAI;cAAC4G,OAAO,EAAC,UAAU;cAAAnB,QAAA,eACtBjE,OAAA,CAACvB,WAAW;gBAAAwF,QAAA,gBACVjE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAApB,QAAA,GAClD,EAAAP,qBAAA,GAAAkB,gBAAgB,CAACI,IAAI,cAAAtB,qBAAA,uBAArBA,qBAAA,CAAuBqC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,GAC9C;gBAAA;kBAAA7B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrE,OAAA,CAAC5B,UAAU;kBAACgH,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA7B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbrE,OAAA,CAACrB,IAAI;kBACHqH,KAAK,EAAC,gCAAgC;kBACtCC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAhC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPrE,OAAA,CAACtB,OAAO;UAACuG,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BrE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,WAAW;UAACC,YAAY;UAAApB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACpB,KAAK;UACJoF,QAAQ,EAAES,UAAU,CAACG,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;UAC/CO,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,gBAEdjE,OAAA,CAAC5B,UAAU;YAACgH,OAAO,EAAC,WAAW;YAACC,YAAY;YAAApB,QAAA,GACzC3D,SAAS,EAAC,KAAG,EAACqE,mBAAmB,CAACC,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAC;UAAA;YAAAR,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAEbrE,OAAA,CAAC5B,UAAU;YAACgH,OAAO,EAAC,OAAO;YAAAnB,QAAA,EACxBW,gBAAgB,CAACF,EAAE,IAAI,GAAG,gBACzB1E,OAAA,CAAAE,SAAA;cAAA+D,QAAA,GAAE,mEAC8D,GAAAN,oBAAA,GAACiB,gBAAgB,CAACF,EAAE,cAAAf,oBAAA,uBAAnBA,oBAAA,CAAqBoC,OAAO,CAAC,CAAC,CAAC,EAAC,iBAChF,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,qEAEzD;YAAA,eAAE,CAAC,GACDnB,gBAAgB,CAACF,EAAE,IAAI,GAAG,gBAC5B1E,OAAA,CAAAE,SAAA;cAAA+D,QAAA,GAAE,uEACkE,GAAAL,oBAAA,GAACgB,gBAAgB,CAACF,EAAE,cAAAd,oBAAA,uBAAnBA,oBAAA,CAAqBmC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAC/E,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,qIAE9D;YAAA,eAAE,CAAC,gBAEH/F,OAAA,CAAAE,SAAA;cAAA+D,QAAA,GAAE,mEAC8D,GAAAJ,oBAAA,GAACe,gBAAgB,CAACF,EAAE,cAAAb,oBAAA,uBAAnBA,oBAAA,CAAqBkC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAC3E,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,+EAE5D,eAAA/F,OAAA;gBAAAiE,QAAA,gBACEjE,OAAA;kBAAAiE,QAAA,EAAI;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClCrE,OAAA;kBAAAiE,QAAA,EAAI;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxCrE,OAAA;kBAAAiE,QAAA,EAAI;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9BrE,OAAA;kBAAAiE,QAAA,EAAI;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA,eACL;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAERrE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,WAAW;UAACC,YAAY;UAAApB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACnB,IAAI;UAAAoF,QAAA,gBACHjE,OAAA,CAAClB,QAAQ;YAAAmF,QAAA,gBACPjE,OAAA,CAACjB,YAAY;cAAAkF,QAAA,eACXjE,OAAA,CAACP,IAAI;gBAACqG,KAAK,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACfrE,OAAA,CAAChB,YAAY;cACXoH,OAAO,EAAE,mBAAmB,EAAAtC,sBAAA,GAAAc,gBAAgB,CAACG,GAAG,cAAAjB,sBAAA,uBAApBA,sBAAA,CAAsBiC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,UAAW;cAChFM,SAAS,EAAC;YAAoD;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXrE,OAAA,CAAClB,QAAQ;YAAAmF,QAAA,gBACPjE,OAAA,CAACjB,YAAY;cAAAkF,QAAA,eACXjE,OAAA,CAACP,IAAI;gBAACqG,KAAK,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACfrE,OAAA,CAAChB,YAAY;cACXoH,OAAO,EAAE,qBAAqB,EAAArC,sBAAA,GAAAa,gBAAgB,CAACI,IAAI,cAAAjB,sBAAA,uBAArBA,sBAAA,CAAuBgC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,GAAI;cAC5EM,SAAS,EAAC;YAAoD;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAMiC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAACxF,iBAAiB,EAAE;MACtB,oBACEd,OAAA,CAACpB,KAAK;QAACoF,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,MAAM7C,SAAS,GAAGV,iBAAiB,CAACkB,KAAK,GACvCF,IAAI,CAACC,KAAK,CAACjB,iBAAiB,CAACkB,KAAK,CAAC,GAAG,IAAI;IAE5C,IAAI,CAACR,SAAS,EAAE;MACd,oBACExB,OAAA,CAACpB,KAAK;QAACoF,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,IAAIkC,WAAW,GAAG,EAAE;IAEpB,IAAI/E,SAAS,CAACU,IAAI,IAAIV,SAAS,CAACU,IAAI,CAACsE,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMC,UAAU,GAAGjF,SAAS,CAACU,IAAI,CAAC,CAAC,CAAC;;MAEpC;MACA,IAAIuE,UAAU,CAACC,CAAC,IAAID,UAAU,CAACE,CAAC,EAAE;QAChCJ,WAAW,GAAGE,UAAU,CAACE,CAAC,CAACC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,MAAM;UACtDC,IAAI,EAAEF,WAAW;UACjBG,UAAU,EAAEP,UAAU,CAACC,CAAC,CAACI,KAAK;QAChC,CAAC,CAAC,CAAC;MACL;IACF;;IAEA;IACA,IAAIP,WAAW,CAACC,MAAM,KAAK,CAAC,IAAIhF,SAAS,CAACU,IAAI,EAAE;MAC9CqE,WAAW,GAAG/E,SAAS,CAACU,IAAI;IAC9B;;IAEA;IACA,IAAIqE,WAAW,CAACC,MAAM,KAAK,CAAC,EAAE;MAC5B,oBACExG,OAAA,CAACpB,KAAK;QAACoF,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACErE,OAAA,CAAC7B,GAAG;MAAA8F,QAAA,eACFjE,OAAA,CAAC1B,KAAK;QAAC2G,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBACzBjE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,IAAI;UAACC,YAAY;UAAApB,QAAA,gBACnCjE,OAAA,CAACT,WAAW;YAAC0F,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACpB,KAAK;UAACoF,QAAQ,EAAC,MAAM;UAACiB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,eACnCjE,OAAA,CAAC5B,UAAU;YAACgH,OAAO,EAAC,OAAO;YAAAnB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAERrE,OAAA,CAACnB,IAAI;UAAAoF,QAAA,EACFsC,WAAW,CAACU,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACL,GAAG,CAAC,CAACM,OAAO,EAAEJ,KAAK,kBAC3C9G,OAAA,CAAClB,QAAQ;YAAAmF,QAAA,gBACPjE,OAAA,CAACjB,YAAY;cAAAkF,QAAA,eACXjE,OAAA,CAACR,UAAU;gBAACsG,KAAK,EAAEgB,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;cAAS;gBAAA5C,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACfrE,OAAA,CAAChB,YAAY;cACXoH,OAAO,EAAE,GAAGU,KAAK,GAAG,CAAC,KAAKI,OAAO,CAACH,IAAI,IAAIG,OAAO,CAACR,CAAC,IAAI,WAAWI,KAAK,GAAG,CAAC,EAAE,EAAG;cAChFT,SAAS,EAAE,eAAe,CAAC,MAAM;gBAC/B,MAAMW,UAAU,GAAGE,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACP,CAAC;gBAClD,OAAO,OAAOK,UAAU,KAAK,QAAQ,GAAGA,UAAU,CAACjB,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;cACvE,CAAC,EAAE,CAAC;YAAG;cAAA7B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACFrE,OAAA,CAAC7B,GAAG;cAAC8G,EAAE,EAAE;gBAAEkC,KAAK,EAAE,KAAK;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAnD,QAAA,eAC/BjE,OAAA,CAAC7B,GAAG;gBACF8G,EAAE,EAAE;kBACFoC,MAAM,EAAE,EAAE;kBACVC,OAAO,EAAER,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,eAAe;kBACrDK,KAAK,EAAE,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,QAAQP,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACP,CAAC,CAAC,KAAK,QAAQ,GAAIO,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACP,CAAC,GAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG;kBAC9Ie,QAAQ,EAAE,IAAI;kBACdC,YAAY,EAAE;gBAChB;cAAE;gBAAAzD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GArBOyC,KAAK;YAAA5C,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsBV,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPrE,OAAA,CAACtB,OAAO;UAACuG,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BrE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,WAAW;UAACC,YAAY;UAAApB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACnB,IAAI;UAAAoF,QAAA,gBACHjE,OAAA,CAAClB,QAAQ;YAAAmF,QAAA,gBACPjE,OAAA,CAACjB,YAAY;cAAAkF,QAAA,eACXjE,OAAA,CAACP,IAAI;gBAACqG,KAAK,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACfrE,OAAA,CAAChB,YAAY;cACXoH,OAAO,EAAC,gBAAgB;cACxBC,SAAS,EAAE;YAAwF;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXrE,OAAA,CAAClB,QAAQ;YAAAmF,QAAA,gBACPjE,OAAA,CAACjB,YAAY;cAAAkF,QAAA,eACXjE,OAAA,CAACP,IAAI;gBAACqG,KAAK,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACfrE,OAAA,CAAChB,YAAY;cACXoH,OAAO,EAAC,mBAAmB;cAC3BC,SAAS,EAAC;YAAiF;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAMuD,uBAAuB,GAAGA,CAAA,KAAM;IACpC,oBACE5H,OAAA,CAAC7B,GAAG;MAAA8F,QAAA,eACFjE,OAAA,CAAC1B,KAAK;QAAC2G,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBACzBjE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,IAAI;UAACC,YAAY;UAAApB,QAAA,gBACnCjE,OAAA,CAACV,QAAQ;YAAC2F,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACpB,KAAK;UAACoF,QAAQ,EAAC,MAAM;UAACiB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,eACnCjE,OAAA,CAAC5B,UAAU;YAACgH,OAAO,EAAC,OAAO;YAAAnB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEPrD,cAAc,CAACuB,iBAAiB,gBAC/BvC,OAAA,CAAC7B,GAAG;UAAC8G,EAAE,EAAE;YAAE4C,SAAS,EAAE,QAAQ;YAAE3B,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EACrC,OAAOjD,cAAc,CAACuB,iBAAiB,KAAK,QAAQ,gBACnDvC,OAAA;YACE8H,GAAG,EAAE,yBAAyB9G,cAAc,CAACuB,iBAAiB,EAAG;YACjEwF,GAAG,EAAC,yBAAyB;YAC7BC,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEZ,MAAM,EAAE;YAAO,CAAE;YAC5Ca,OAAO,EAAGC,CAAC,IAAK;cACd7F,OAAO,CAACD,KAAK,CAAC,yCAAyC,CAAC;cACxD8F,CAAC,CAACC,MAAM,CAACJ,KAAK,CAACK,OAAO,GAAG,MAAM;YACjC;UAAE;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACArD,cAAc,CAACuB,iBAAiB,CAACZ,IAAI,KAAK,QAAQ,gBACpD3B,OAAA;YACEuB,GAAG,EAAEL,oBAAqB;YAC1B8G,KAAK,EAAE;cAAEb,KAAK,EAAE,MAAM;cAAEE,MAAM,EAAE;YAAQ;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAEFrE,OAAA,CAAC5B,UAAU;YAAC0H,KAAK,EAAC,OAAO;YAAA7B,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENrE,OAAA,CAAC7B,GAAG;UAAC8G,EAAE,EAAE;YAAE4C,SAAS,EAAE,QAAQ;YAAE3C,CAAC,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBACrCjE,OAAA,CAAC5B,UAAU;YAAC0H,KAAK,EAAC,gBAAgB;YAAA7B,QAAA,EAC/BvD,SAAS,KAAK,CAAC,GAAG,0BAA0B,GAAG;UAA6B;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,EACZ3D,SAAS,KAAK,CAAC,iBACdV,OAAA,CAAC3B,MAAM;YACL+G,OAAO,EAAC,UAAU;YAClBkD,OAAO,EAAEA,CAAA,KAAMtF,iBAAiB,CAAC,mBAAmB,CAAE;YACtDiC,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,EACf;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAMkE,mBAAmB,GAAGA,CAAA,KAAM;IAChC,oBACEvI,OAAA,CAAC7B,GAAG;MAAA8F,QAAA,eACFjE,OAAA,CAAC1B,KAAK;QAAC2G,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAAlB,QAAA,gBACzBjE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,IAAI;UAACC,YAAY;UAAApB,QAAA,gBACnCjE,OAAA,CAACV,QAAQ;YAAC2F,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAArB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACpB,KAAK;UAACoF,QAAQ,EAAC,MAAM;UAACiB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAAlB,QAAA,eACnCjE,OAAA,CAAC5B,UAAU;YAACgH,OAAO,EAAC,OAAO;YAAAnB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEPrD,cAAc,CAACwB,SAAS,gBACvBxC,OAAA,CAAC7B,GAAG;UAAC8G,EAAE,EAAE;YAAE4C,SAAS,EAAE,QAAQ;YAAE3B,EAAE,EAAE;UAAE,CAAE;UAAAjC,QAAA,EACrC,OAAOjD,cAAc,CAACwB,SAAS,KAAK,QAAQ,gBAC3CxC,OAAA;YACE8H,GAAG,EAAE,yBAAyB9G,cAAc,CAACwB,SAAS,EAAG;YACzDuF,GAAG,EAAC,gBAAgB;YACpBC,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEZ,MAAM,EAAE;YAAO,CAAE;YAC5Ca,OAAO,EAAGC,CAAC,IAAK;cACd7F,OAAO,CAACD,KAAK,CAAC,gCAAgC,CAAC;cAC/C8F,CAAC,CAACC,MAAM,CAACJ,KAAK,CAACK,OAAO,GAAG,MAAM;YACjC;UAAE;YAAAnE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACArD,cAAc,CAACwB,SAAS,CAACb,IAAI,KAAK,QAAQ,gBAC5C3B,OAAA;YACEuB,GAAG,EAAEJ,YAAa;YAClB6G,KAAK,EAAE;cAAEb,KAAK,EAAE,MAAM;cAAEE,MAAM,EAAE;YAAQ;UAAE;YAAAnD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAEFrE,OAAA,CAAC5B,UAAU;YAAC0H,KAAK,EAAC,OAAO;YAAA7B,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENrE,OAAA,CAAC7B,GAAG;UAAC8G,EAAE,EAAE;YAAE4C,SAAS,EAAE,QAAQ;YAAE3C,CAAC,EAAE;UAAE,CAAE;UAAAjB,QAAA,gBACrCjE,OAAA,CAAC5B,UAAU;YAAC0H,KAAK,EAAC,gBAAgB;YAAA7B,QAAA,EAC/BvD,SAAS,KAAK,CAAC,GAAG,0BAA0B,GAAG;UAA6B;YAAAwD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,EACZ3D,SAAS,KAAK,CAAC,iBACdV,OAAA,CAAC3B,MAAM;YACL+G,OAAO,EAAC,UAAU;YAClBkD,OAAO,EAAEA,CAAA,KAAMtF,iBAAiB,CAAC,WAAW,CAAE;YAC9CiC,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAjC,QAAA,EACf;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAEDrE,OAAA,CAACtB,OAAO;UAACuG,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAjC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BrE,OAAA,CAAC5B,UAAU;UAACgH,OAAO,EAAC,WAAW;UAACC,YAAY;UAAApB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbrE,OAAA,CAACnB,IAAI;UAAAoF,QAAA,gBACHjE,OAAA,CAAClB,QAAQ;YAAAmF,QAAA,gBACPjE,OAAA,CAACjB,YAAY;cAAAkF,QAAA,eACXjE,OAAA,CAACP,IAAI;gBAACqG,KAAK,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACfrE,OAAA,CAAChB,YAAY;cACXoH,OAAO,EAAC,qBAAqB;cAC7BC,SAAS,EAAC;YAA2E;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXrE,OAAA,CAAClB,QAAQ;YAAAmF,QAAA,gBACPjE,OAAA,CAACjB,YAAY;cAAAkF,QAAA,eACXjE,OAAA,CAACN,OAAO;gBAACoG,KAAK,EAAC;cAAS;gBAAA5B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACfrE,OAAA,CAAChB,YAAY;cACXoH,OAAO,EAAC,uBAAuB;cAC/BC,SAAS,EAAC;YAAgG;cAAAnC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,oBACErE,OAAA,CAAC7B,GAAG;IAAA8F,QAAA,gBACFjE,OAAA,CAAC5B,UAAU;MAACgH,OAAO,EAAC,IAAI;MAACC,YAAY;MAAApB,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbrE,OAAA,CAAC5B,UAAU;MAACgH,OAAO,EAAC,OAAO;MAACU,KAAK,EAAC,gBAAgB;MAAC0C,SAAS;MAAAvE,QAAA,EAAC;IAG7D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbrE,OAAA,CAAC1B,KAAK;MAAC2G,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAAlB,QAAA,eACnBjE,OAAA,CAACd,IAAI;QACHuJ,KAAK,EAAE/H,SAAU;QACjBgI,QAAQ,EAAE7F,eAAgB;QAC1BuC,OAAO,EAAC,WAAW;QACnBuD,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QAAA3E,QAAA,gBAEnBjE,OAAA,CAACf,GAAG;UAAC4J,IAAI,eAAE7I,OAAA,CAACX,QAAQ;YAAA6E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC2B,KAAK,EAAC;QAAa;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CrE,OAAA,CAACf,GAAG;UAAC4J,IAAI,eAAE7I,OAAA,CAACV,QAAQ;YAAA4E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC2B,KAAK,EAAC;QAAa;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CrE,OAAA,CAACf,GAAG;UAAC4J,IAAI,eAAE7I,OAAA,CAACT,WAAW;YAAA2E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC2B,KAAK,EAAC;QAAU;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CrE,OAAA,CAACf,GAAG;UAAC4J,IAAI,eAAE7I,OAAA,CAACR,UAAU;YAAA0E,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC2B,KAAK,EAAC;QAAW;UAAA9B,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEP3D,SAAS,KAAK,CAAC,IAAI2C,wBAAwB,CAAC,CAAC,EAC7C3C,SAAS,KAAK,CAAC,IAAIkH,uBAAuB,CAAC,CAAC,EAC5ClH,SAAS,KAAK,CAAC,IAAI4F,uBAAuB,CAAC,CAAC,EAC5C5F,SAAS,KAAK,CAAC,IAAI6H,mBAAmB,CAAC,CAAC,eAEzCvI,OAAA,CAAC7B,GAAG;MAAC8G,EAAE,EAAE;QAAEoD,OAAO,EAAE,MAAM;QAAES,cAAc,EAAE,eAAe;QAAE5C,EAAE,EAAE;MAAE,CAAE;MAAAjC,QAAA,gBACnEjE,OAAA,CAAC3B,MAAM;QACL+G,OAAO,EAAC,UAAU;QAClBkD,OAAO,EAAEjI,MAAO;QAChB0I,SAAS,eAAE/I,OAAA,CAACb,SAAS;UAAA+E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAC1B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETrE,OAAA,CAAC3B,MAAM;QACL+G,OAAO,EAAC,WAAW;QACnBU,KAAK,EAAC,SAAS;QACfwC,OAAO,EAAElI,MAAO;QAChB4I,OAAO,eAAEhJ,OAAA,CAACZ,YAAY;UAAA8E,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC5D,EAAA,CAjoBIN,eAAe;AAAA8I,EAAA,GAAf9I,eAAe;AAmoBrB,eAAeA,eAAe;AAAC,IAAA8I,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}