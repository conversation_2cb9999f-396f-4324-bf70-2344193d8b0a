# plotly.js-dist

Ready-to-use plotly.js distributed bundle.

Contains trace modules `bar`, `barpolar`, `box`, `candlestick`, `carpet`, `choropleth`, `choroplethmap`, `choroplethmapbox`, `cone`, `contour`, `contourcarpet`, `densitymap`, `densitymapbox`, `funnel`, `funnelarea`, `heatmap`, `histogram`, `histogram2d`, `histogram2dcontour`, `icicle`, `image`, `indicator`, `isosurface`, `mesh3d`, `ohlc`, `parcats`, `parcoords`, `pie`, `sankey`, `scatter`, `scatter3d`, `scattercarpet`, `scattergeo`, `scattergl`, `scattermap`, `scattermapbox`, `scatterpolar`, `scatterpolargl`, `scattersmith`, `scatterternary`, `splom`, `streamtube`, `sunburst`, `surface`, `table`, `treemap`, `violin`, `volume` and `waterfall`.

For more info on plotly.js, go to https://github.com/plotly/plotly.js#readme

## Installation

```
npm install plotly.js-dist
```
## Usage

```js
// ES6 module
import Plotly from 'plotly.js-dist'

// CommonJS
var Plotly = require('plotly.js-dist')
```

## Copyright and license

Code and documentation copyright 2025 Plotly, Inc.

Code released under the [MIT license](https://github.com/plotly/plotly.js/blob/master/LICENSE).

Docs released under the [Creative Commons license](https://github.com/plotly/documentation/blob/source/LICENSE).

Please visit [complete list of dependencies](https://www.npmjs.com/package/plotly.js/v/3.0.1?activeTab=dependencies).