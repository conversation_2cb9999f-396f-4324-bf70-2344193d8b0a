{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSwitchUtilityClass(slot) {\n  return generateUtilityClass('MuiSwitch', slot);\n}\nconst switchClasses = generateUtilityClasses('MuiSwitch', ['root', 'edgeStart', 'edgeEnd', 'switchBase', 'colorPrimary', 'colorSecondary', 'sizeSmall', 'sizeMedium', 'checked', 'disabled', 'input', 'thumb', 'track']);\nexport default switchClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSwitchUtilityClass", "slot", "switchClasses"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/material/Switch/switchClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSwitchUtilityClass(slot) {\n  return generateUtilityClass('MuiSwitch', slot);\n}\nconst switchClasses = generateUtilityClasses('MuiSwitch', ['root', 'edgeStart', 'edgeEnd', 'switchBase', 'colorPrimary', 'colorSecondary', 'sizeSmall', 'sizeMedium', 'checked', 'disabled', 'input', 'thumb', 'track']);\nexport default switchClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,qBAAqBA,CAACC,IAAI,EAAE;EAC1C,OAAOF,oBAAoB,CAAC,WAAW,EAAEE,IAAI,CAAC;AAChD;AACA,MAAMC,aAAa,GAAGJ,sBAAsB,CAAC,WAAW,EAAE,CAAC,MAAM,EAAE,WAAW,EAAE,SAAS,EAAE,YAAY,EAAE,cAAc,EAAE,gBAAgB,EAAE,WAAW,EAAE,YAAY,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;AACxN,eAAeI,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}