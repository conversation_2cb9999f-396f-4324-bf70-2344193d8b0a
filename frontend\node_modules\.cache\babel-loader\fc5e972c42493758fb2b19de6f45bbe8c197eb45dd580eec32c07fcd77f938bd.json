{"ast": null, "code": "'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"boundaryCount\", \"componentName\", \"count\", \"defaultPage\", \"disabled\", \"hideNextButton\", \"hidePrevButton\", \"onChange\", \"page\", \"showFirstButton\", \"showLastButton\", \"siblingCount\"];\nimport useControlled from '@mui/utils/useControlled';\nexport default function usePagination(props = {}) {\n  // keep default values in sync with @default tags in Pagination.propTypes\n  const {\n      boundaryCount = 1,\n      componentName = 'usePagination',\n      count = 1,\n      defaultPage = 1,\n      disabled = false,\n      hideNextButton = false,\n      hidePrevButton = false,\n      onChange: handleChange,\n      page: pageProp,\n      showFirstButton = false,\n      showLastButton = false,\n      siblingCount = 1\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [page, setPageState] = useControlled({\n    controlled: pageProp,\n    default: defaultPage,\n    name: componentName,\n    state: 'page'\n  });\n  const handleClick = (event, value) => {\n    if (!pageProp) {\n      setPageState(value);\n    }\n    if (handleChange) {\n      handleChange(event, value);\n    }\n  };\n\n  // https://dev.to/namirsab/comment/2050\n  const range = (start, end) => {\n    const length = end - start + 1;\n    return Array.from({\n      length\n    }, (_, i) => start + i);\n  };\n  const startPages = range(1, Math.min(boundaryCount, count));\n  const endPages = range(Math.max(count - boundaryCount + 1, boundaryCount + 1), count);\n  const siblingsStart = Math.max(Math.min(\n  // Natural start\n  page - siblingCount,\n  // Lower boundary when page is high\n  count - boundaryCount - siblingCount * 2 - 1),\n  // Greater than startPages\n  boundaryCount + 2);\n  const siblingsEnd = Math.min(Math.max(\n  // Natural end\n  page + siblingCount,\n  // Upper boundary when page is low\n  boundaryCount + siblingCount * 2 + 2),\n  // Less than endPages\n  endPages.length > 0 ? endPages[0] - 2 : count - 1);\n\n  // Basic list of items to render\n  // for example itemList = ['first', 'previous', 1, 'ellipsis', 4, 5, 6, 'ellipsis', 10, 'next', 'last']\n  const itemList = [...(showFirstButton ? ['first'] : []), ...(hidePrevButton ? [] : ['previous']), ...startPages,\n  // Start ellipsis\n  // eslint-disable-next-line no-nested-ternary\n  ...(siblingsStart > boundaryCount + 2 ? ['start-ellipsis'] : boundaryCount + 1 < count - boundaryCount ? [boundaryCount + 1] : []),\n  // Sibling pages\n  ...range(siblingsStart, siblingsEnd),\n  // End ellipsis\n  // eslint-disable-next-line no-nested-ternary\n  ...(siblingsEnd < count - boundaryCount - 1 ? ['end-ellipsis'] : count - boundaryCount > boundaryCount ? [count - boundaryCount] : []), ...endPages, ...(hideNextButton ? [] : ['next']), ...(showLastButton ? ['last'] : [])];\n\n  // Map the button type to its page number\n  const buttonPage = type => {\n    switch (type) {\n      case 'first':\n        return 1;\n      case 'previous':\n        return page - 1;\n      case 'next':\n        return page + 1;\n      case 'last':\n        return count;\n      default:\n        return null;\n    }\n  };\n\n  // Convert the basic item list to PaginationItem props objects\n  const items = itemList.map(item => {\n    return typeof item === 'number' ? {\n      onClick: event => {\n        handleClick(event, item);\n      },\n      type: 'page',\n      page: item,\n      selected: item === page,\n      disabled,\n      'aria-current': item === page ? 'true' : undefined\n    } : {\n      onClick: event => {\n        handleClick(event, buttonPage(item));\n      },\n      type: item,\n      page: buttonPage(item),\n      selected: false,\n      disabled: disabled || item.indexOf('ellipsis') === -1 && (item === 'next' || item === 'last' ? page >= count : page <= 1)\n    };\n  });\n  return _extends({\n    items\n  }, other);\n}", "map": {"version": 3, "names": ["_extends", "_objectWithoutPropertiesLoose", "_excluded", "useControlled", "usePagination", "props", "boundaryCount", "componentName", "count", "defaultPage", "disabled", "hideNextButton", "hidePrevButton", "onChange", "handleChange", "page", "pageProp", "showFirstButton", "showLastButton", "siblingCount", "other", "setPageState", "controlled", "default", "name", "state", "handleClick", "event", "value", "range", "start", "end", "length", "Array", "from", "_", "i", "startPages", "Math", "min", "endPages", "max", "siblingsStart", "siblingsEnd", "itemList", "buttonPage", "type", "items", "map", "item", "onClick", "selected", "undefined", "indexOf"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/material/usePagination/usePagination.js"], "sourcesContent": ["'use client';\n\nimport _extends from \"@babel/runtime/helpers/esm/extends\";\nimport _objectWithoutPropertiesLoose from \"@babel/runtime/helpers/esm/objectWithoutPropertiesLoose\";\nconst _excluded = [\"boundaryCount\", \"componentName\", \"count\", \"defaultPage\", \"disabled\", \"hideNextButton\", \"hidePrevButton\", \"onChange\", \"page\", \"showFirstButton\", \"showLastButton\", \"siblingCount\"];\nimport useControlled from '@mui/utils/useControlled';\nexport default function usePagination(props = {}) {\n  // keep default values in sync with @default tags in Pagination.propTypes\n  const {\n      boundaryCount = 1,\n      componentName = 'usePagination',\n      count = 1,\n      defaultPage = 1,\n      disabled = false,\n      hideNextButton = false,\n      hidePrevButton = false,\n      onChange: handleChange,\n      page: pageProp,\n      showFirstButton = false,\n      showLastButton = false,\n      siblingCount = 1\n    } = props,\n    other = _objectWithoutPropertiesLoose(props, _excluded);\n  const [page, setPageState] = useControlled({\n    controlled: pageProp,\n    default: defaultPage,\n    name: componentName,\n    state: 'page'\n  });\n  const handleClick = (event, value) => {\n    if (!pageProp) {\n      setPageState(value);\n    }\n    if (handleChange) {\n      handleChange(event, value);\n    }\n  };\n\n  // https://dev.to/namirsab/comment/2050\n  const range = (start, end) => {\n    const length = end - start + 1;\n    return Array.from({\n      length\n    }, (_, i) => start + i);\n  };\n  const startPages = range(1, Math.min(boundaryCount, count));\n  const endPages = range(Math.max(count - boundaryCount + 1, boundaryCount + 1), count);\n  const siblingsStart = Math.max(Math.min(\n  // Natural start\n  page - siblingCount,\n  // Lower boundary when page is high\n  count - boundaryCount - siblingCount * 2 - 1),\n  // Greater than startPages\n  boundaryCount + 2);\n  const siblingsEnd = Math.min(Math.max(\n  // Natural end\n  page + siblingCount,\n  // Upper boundary when page is low\n  boundaryCount + siblingCount * 2 + 2),\n  // Less than endPages\n  endPages.length > 0 ? endPages[0] - 2 : count - 1);\n\n  // Basic list of items to render\n  // for example itemList = ['first', 'previous', 1, 'ellipsis', 4, 5, 6, 'ellipsis', 10, 'next', 'last']\n  const itemList = [...(showFirstButton ? ['first'] : []), ...(hidePrevButton ? [] : ['previous']), ...startPages,\n  // Start ellipsis\n  // eslint-disable-next-line no-nested-ternary\n  ...(siblingsStart > boundaryCount + 2 ? ['start-ellipsis'] : boundaryCount + 1 < count - boundaryCount ? [boundaryCount + 1] : []),\n  // Sibling pages\n  ...range(siblingsStart, siblingsEnd),\n  // End ellipsis\n  // eslint-disable-next-line no-nested-ternary\n  ...(siblingsEnd < count - boundaryCount - 1 ? ['end-ellipsis'] : count - boundaryCount > boundaryCount ? [count - boundaryCount] : []), ...endPages, ...(hideNextButton ? [] : ['next']), ...(showLastButton ? ['last'] : [])];\n\n  // Map the button type to its page number\n  const buttonPage = type => {\n    switch (type) {\n      case 'first':\n        return 1;\n      case 'previous':\n        return page - 1;\n      case 'next':\n        return page + 1;\n      case 'last':\n        return count;\n      default:\n        return null;\n    }\n  };\n\n  // Convert the basic item list to PaginationItem props objects\n  const items = itemList.map(item => {\n    return typeof item === 'number' ? {\n      onClick: event => {\n        handleClick(event, item);\n      },\n      type: 'page',\n      page: item,\n      selected: item === page,\n      disabled,\n      'aria-current': item === page ? 'true' : undefined\n    } : {\n      onClick: event => {\n        handleClick(event, buttonPage(item));\n      },\n      type: item,\n      page: buttonPage(item),\n      selected: false,\n      disabled: disabled || item.indexOf('ellipsis') === -1 && (item === 'next' || item === 'last' ? page >= count : page <= 1)\n    };\n  });\n  return _extends({\n    items\n  }, other);\n}"], "mappings": "AAAA,YAAY;;AAEZ,OAAOA,QAAQ,MAAM,oCAAoC;AACzD,OAAOC,6BAA6B,MAAM,yDAAyD;AACnG,MAAMC,SAAS,GAAG,CAAC,eAAe,EAAE,eAAe,EAAE,OAAO,EAAE,aAAa,EAAE,UAAU,EAAE,gBAAgB,EAAE,gBAAgB,EAAE,UAAU,EAAE,MAAM,EAAE,iBAAiB,EAAE,gBAAgB,EAAE,cAAc,CAAC;AACrM,OAAOC,aAAa,MAAM,0BAA0B;AACpD,eAAe,SAASC,aAAaA,CAACC,KAAK,GAAG,CAAC,CAAC,EAAE;EAChD;EACA,MAAM;MACFC,aAAa,GAAG,CAAC;MACjBC,aAAa,GAAG,eAAe;MAC/BC,KAAK,GAAG,CAAC;MACTC,WAAW,GAAG,CAAC;MACfC,QAAQ,GAAG,KAAK;MAChBC,cAAc,GAAG,KAAK;MACtBC,cAAc,GAAG,KAAK;MACtBC,QAAQ,EAAEC,YAAY;MACtBC,IAAI,EAAEC,QAAQ;MACdC,eAAe,GAAG,KAAK;MACvBC,cAAc,GAAG,KAAK;MACtBC,YAAY,GAAG;IACjB,CAAC,GAAGd,KAAK;IACTe,KAAK,GAAGnB,6BAA6B,CAACI,KAAK,EAAEH,SAAS,CAAC;EACzD,MAAM,CAACa,IAAI,EAAEM,YAAY,CAAC,GAAGlB,aAAa,CAAC;IACzCmB,UAAU,EAAEN,QAAQ;IACpBO,OAAO,EAAEd,WAAW;IACpBe,IAAI,EAAEjB,aAAa;IACnBkB,KAAK,EAAE;EACT,CAAC,CAAC;EACF,MAAMC,WAAW,GAAGA,CAACC,KAAK,EAAEC,KAAK,KAAK;IACpC,IAAI,CAACZ,QAAQ,EAAE;MACbK,YAAY,CAACO,KAAK,CAAC;IACrB;IACA,IAAId,YAAY,EAAE;MAChBA,YAAY,CAACa,KAAK,EAAEC,KAAK,CAAC;IAC5B;EACF,CAAC;;EAED;EACA,MAAMC,KAAK,GAAGA,CAACC,KAAK,EAAEC,GAAG,KAAK;IAC5B,MAAMC,MAAM,GAAGD,GAAG,GAAGD,KAAK,GAAG,CAAC;IAC9B,OAAOG,KAAK,CAACC,IAAI,CAAC;MAChBF;IACF,CAAC,EAAE,CAACG,CAAC,EAAEC,CAAC,KAAKN,KAAK,GAAGM,CAAC,CAAC;EACzB,CAAC;EACD,MAAMC,UAAU,GAAGR,KAAK,CAAC,CAAC,EAAES,IAAI,CAACC,GAAG,CAACjC,aAAa,EAAEE,KAAK,CAAC,CAAC;EAC3D,MAAMgC,QAAQ,GAAGX,KAAK,CAACS,IAAI,CAACG,GAAG,CAACjC,KAAK,GAAGF,aAAa,GAAG,CAAC,EAAEA,aAAa,GAAG,CAAC,CAAC,EAAEE,KAAK,CAAC;EACrF,MAAMkC,aAAa,GAAGJ,IAAI,CAACG,GAAG,CAACH,IAAI,CAACC,GAAG;EACvC;EACAxB,IAAI,GAAGI,YAAY;EACnB;EACAX,KAAK,GAAGF,aAAa,GAAGa,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;EAC7C;EACAb,aAAa,GAAG,CAAC,CAAC;EAClB,MAAMqC,WAAW,GAAGL,IAAI,CAACC,GAAG,CAACD,IAAI,CAACG,GAAG;EACrC;EACA1B,IAAI,GAAGI,YAAY;EACnB;EACAb,aAAa,GAAGa,YAAY,GAAG,CAAC,GAAG,CAAC,CAAC;EACrC;EACAqB,QAAQ,CAACR,MAAM,GAAG,CAAC,GAAGQ,QAAQ,CAAC,CAAC,CAAC,GAAG,CAAC,GAAGhC,KAAK,GAAG,CAAC,CAAC;;EAElD;EACA;EACA,MAAMoC,QAAQ,GAAG,CAAC,IAAI3B,eAAe,GAAG,CAAC,OAAO,CAAC,GAAG,EAAE,CAAC,EAAE,IAAIL,cAAc,GAAG,EAAE,GAAG,CAAC,UAAU,CAAC,CAAC,EAAE,GAAGyB,UAAU;EAC/G;EACA;EACA,IAAIK,aAAa,GAAGpC,aAAa,GAAG,CAAC,GAAG,CAAC,gBAAgB,CAAC,GAAGA,aAAa,GAAG,CAAC,GAAGE,KAAK,GAAGF,aAAa,GAAG,CAACA,aAAa,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;EAClI;EACA,GAAGuB,KAAK,CAACa,aAAa,EAAEC,WAAW,CAAC;EACpC;EACA;EACA,IAAIA,WAAW,GAAGnC,KAAK,GAAGF,aAAa,GAAG,CAAC,GAAG,CAAC,cAAc,CAAC,GAAGE,KAAK,GAAGF,aAAa,GAAGA,aAAa,GAAG,CAACE,KAAK,GAAGF,aAAa,CAAC,GAAG,EAAE,CAAC,EAAE,GAAGkC,QAAQ,EAAE,IAAI7B,cAAc,GAAG,EAAE,GAAG,CAAC,MAAM,CAAC,CAAC,EAAE,IAAIO,cAAc,GAAG,CAAC,MAAM,CAAC,GAAG,EAAE,CAAC,CAAC;;EAE9N;EACA,MAAM2B,UAAU,GAAGC,IAAI,IAAI;IACzB,QAAQA,IAAI;MACV,KAAK,OAAO;QACV,OAAO,CAAC;MACV,KAAK,UAAU;QACb,OAAO/B,IAAI,GAAG,CAAC;MACjB,KAAK,MAAM;QACT,OAAOA,IAAI,GAAG,CAAC;MACjB,KAAK,MAAM;QACT,OAAOP,KAAK;MACd;QACE,OAAO,IAAI;IACf;EACF,CAAC;;EAED;EACA,MAAMuC,KAAK,GAAGH,QAAQ,CAACI,GAAG,CAACC,IAAI,IAAI;IACjC,OAAO,OAAOA,IAAI,KAAK,QAAQ,GAAG;MAChCC,OAAO,EAAEvB,KAAK,IAAI;QAChBD,WAAW,CAACC,KAAK,EAAEsB,IAAI,CAAC;MAC1B,CAAC;MACDH,IAAI,EAAE,MAAM;MACZ/B,IAAI,EAAEkC,IAAI;MACVE,QAAQ,EAAEF,IAAI,KAAKlC,IAAI;MACvBL,QAAQ;MACR,cAAc,EAAEuC,IAAI,KAAKlC,IAAI,GAAG,MAAM,GAAGqC;IAC3C,CAAC,GAAG;MACFF,OAAO,EAAEvB,KAAK,IAAI;QAChBD,WAAW,CAACC,KAAK,EAAEkB,UAAU,CAACI,IAAI,CAAC,CAAC;MACtC,CAAC;MACDH,IAAI,EAAEG,IAAI;MACVlC,IAAI,EAAE8B,UAAU,CAACI,IAAI,CAAC;MACtBE,QAAQ,EAAE,KAAK;MACfzC,QAAQ,EAAEA,QAAQ,IAAIuC,IAAI,CAACI,OAAO,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,KAAKJ,IAAI,KAAK,MAAM,IAAIA,IAAI,KAAK,MAAM,GAAGlC,IAAI,IAAIP,KAAK,GAAGO,IAAI,IAAI,CAAC;IAC1H,CAAC;EACH,CAAC,CAAC;EACF,OAAOf,QAAQ,CAAC;IACd+C;EACF,CAAC,EAAE3B,KAAK,CAAC;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}