{"ast": null, "code": "import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSkeletonUtilityClass(slot) {\n  return generateUtilityClass('MuiSkeleton', slot);\n}\nconst skeletonClasses = generateUtilityClasses('MuiSkeleton', ['root', 'text', 'rectangular', 'rounded', 'circular', 'pulse', 'wave', 'withChildren', 'fitContent', 'heightAuto']);\nexport default skeletonClasses;", "map": {"version": 3, "names": ["generateUtilityClasses", "generateUtilityClass", "getSkeletonUtilityClass", "slot", "skeletonClasses"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/material/Skeleton/skeletonClasses.js"], "sourcesContent": ["import generateUtilityClasses from '@mui/utils/generateUtilityClasses';\nimport generateUtilityClass from '@mui/utils/generateUtilityClass';\nexport function getSkeletonUtilityClass(slot) {\n  return generateUtilityClass('MuiSkeleton', slot);\n}\nconst skeletonClasses = generateUtilityClasses('MuiSkeleton', ['root', 'text', 'rectangular', 'rounded', 'circular', 'pulse', 'wave', 'withChildren', 'fitContent', 'heightAuto']);\nexport default skeletonClasses;"], "mappings": "AAAA,OAAOA,sBAAsB,MAAM,mCAAmC;AACtE,OAAOC,oBAAoB,MAAM,iCAAiC;AAClE,OAAO,SAASC,uBAAuBA,CAACC,IAAI,EAAE;EAC5C,OAAOF,oBAAoB,CAAC,aAAa,EAAEE,IAAI,CAAC;AAClD;AACA,MAAMC,eAAe,GAAGJ,sBAAsB,CAAC,aAAa,EAAE,CAAC,MAAM,EAAE,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,UAAU,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;AAClL,eAAeI,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}