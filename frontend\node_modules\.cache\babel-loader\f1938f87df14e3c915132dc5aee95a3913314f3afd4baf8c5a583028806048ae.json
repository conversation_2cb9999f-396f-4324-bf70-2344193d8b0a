{"ast": null, "code": "import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst FormControlContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  FormControlContext.displayName = 'FormControlContext';\n}\nexport default FormControlContext;", "map": {"version": 3, "names": ["React", "FormControlContext", "createContext", "undefined", "process", "env", "NODE_ENV", "displayName"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/material/FormControl/FormControlContext.js"], "sourcesContent": ["import * as React from 'react';\n/**\n * @ignore - internal component.\n */\nconst FormControlContext = /*#__PURE__*/React.createContext(undefined);\nif (process.env.NODE_ENV !== 'production') {\n  FormControlContext.displayName = 'FormControlContext';\n}\nexport default FormControlContext;"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,aAAaD,KAAK,CAACE,aAAa,CAACC,SAAS,CAAC;AACtE,IAAIC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY,EAAE;EACzCL,kBAAkB,CAACM,WAAW,GAAG,oBAAoB;AACvD;AACA,eAAeN,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}