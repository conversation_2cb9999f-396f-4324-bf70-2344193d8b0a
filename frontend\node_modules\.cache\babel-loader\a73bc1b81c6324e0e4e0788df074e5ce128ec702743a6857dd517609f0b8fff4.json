{"ast": null, "code": "var _jsxFileName = \"C:\\\\Users\\\\<USER>\\\\Pumptimemodel\\\\frontend\\\\src\\\\components\\\\ModelEvaluation.js\",\n  _s = $RefreshSig$();\nimport React, { useState, useEffect } from 'react';\nimport Plot from 'react-plotly.js';\nimport { Box, Typography, Button, Paper, Grid, Card, CardContent, Divider, Chip, Alert, List, ListItem, ListItemIcon, ListItemText, Tab, Tabs } from '@mui/material';\nimport { ArrowBack, ArrowForward, BarChart, Timeline, BubbleChart, TrendingUp, Info, Warning, CheckCircle, Error } from '@mui/icons-material';\nimport toast from 'react-hot-toast';\nimport { apiService } from '../services/apiService';\nimport { jsxDEV as _jsxDEV, Fragment as _Fragment } from \"react/jsx-dev-runtime\";\nconst ModelEvaluation = ({\n  onNext,\n  onBack,\n  bestModel,\n  setLoading,\n  setError\n}) => {\n  _s();\n  const [activeTab, setActiveTab] = useState(0);\n  const [modelPerformance, setModelPerformance] = useState(null);\n  const [featureImportance, setFeatureImportance] = useState(null);\n  const [visualizations, setVisualizations] = useState({});\n  useEffect(() => {\n    fetchModelPerformance();\n    fetchFeatureImportance();\n  }, [bestModel]);\n  const fetchModelPerformance = async () => {\n    setLoading(true);\n    try {\n      const result = await apiService.getModelPerformance();\n      setModelPerformance(result);\n    } catch (error) {\n      setError('Failed to fetch model performance: ' + error.message);\n      toast.error('Failed to fetch model performance');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const fetchFeatureImportance = async () => {\n    setLoading(true);\n    try {\n      const result = await apiService.getFeatureImportance();\n      setFeatureImportance(result);\n    } catch (error) {\n      setError('Failed to fetch feature importance: ' + error.message);\n      toast.error('Failed to fetch feature importance');\n    } finally {\n      setLoading(false);\n    }\n  };\n  const handleTabChange = (_, newValue) => {\n    setActiveTab(newValue);\n\n    // Load visualization data for the selected tab if not already loaded\n    if (newValue === 1 && !visualizations.predictionScatter) {\n      loadVisualization('predictionScatter');\n    } else if (newValue === 2 && !visualizations.featureImportance) {\n      loadVisualization('featureImportance');\n    } else if (newValue === 3 && !visualizations.residuals) {\n      loadVisualization('residuals');\n    }\n  };\n  const loadVisualization = async vizType => {\n    setLoading(true);\n    try {\n      let result;\n      switch (vizType) {\n        case 'predictionScatter':\n          result = await apiService.generateVisualization('prediction_scatter');\n          // Handle both image and Plotly JSON responses\n          if (result.image) {\n            result = result.image;\n          } else if (result.chart) {\n            result = {\n              type: 'plotly',\n              chart: result.chart\n            };\n          }\n          break;\n        case 'featureImportance':\n          result = await apiService.generateVisualization('feature_importance');\n          break;\n        case 'residuals':\n          result = await apiService.generateVisualization('residuals_plot');\n          // Handle both image and Plotly JSON responses\n          if (result.image) {\n            result = result.image;\n          } else if (result.chart) {\n            result = {\n              type: 'plotly',\n              chart: result.chart\n            };\n          }\n          break;\n        default:\n          return;\n      }\n      setVisualizations(prev => ({\n        ...prev,\n        [vizType]: result\n      }));\n    } catch (error) {\n      console.error(`Failed to load ${vizType} visualization:`, error);\n      setError(`Failed to load ${vizType} visualization: ${error.message}`);\n    } finally {\n      setLoading(false);\n    }\n  };\n  const renderPerformanceMetrics = () => {\n    var _modelPerformance$das, _bestModelMetrics$r, _bestModelMetrics$rms, _bestModelMetrics$mae, _bestModelMetrics$map, _bestModelMetrics$r2, _bestModelMetrics$r3, _bestModelMetrics$r4, _bestModelMetrics$mae2, _bestModelMetrics$map2;\n    if (!modelPerformance) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Loading model performance metrics...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 135,\n        columnNumber: 9\n      }, this);\n    }\n    const metrics = (_modelPerformance$das = modelPerformance.dashboard) !== null && _modelPerformance$das !== void 0 && _modelPerformance$das.rmse_comparison ? JSON.parse(modelPerformance.dashboard.rmse_comparison) : null;\n    if (!metrics) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No performance metrics available for the model.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 146,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Helper function to get color based on R² score\n    const getR2Color = r2 => {\n      if (r2 >= 0.9) return 'success';\n      if (r2 >= 0.7) return 'primary';\n      if (r2 >= 0.5) return 'warning';\n      return 'error';\n    };\n\n    // Helper function to get interpretation of R² score\n    const getR2Interpretation = r2 => {\n      if (r2 >= 0.9) return 'Excellent fit';\n      if (r2 >= 0.7) return 'Good fit';\n      if (r2 >= 0.5) return 'Moderate fit';\n      if (r2 >= 0.0) return 'Poor fit';\n      return 'Very poor fit';\n    };\n\n    // Extract metrics for the best model\n    const bestModelMetrics = modelPerformance.best_model_metrics || {\n      r2: 0.7,\n      rmse: 45,\n      mae: 35,\n      mape: 15\n    };\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(BarChart, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 181,\n            columnNumber: 13\n          }, this), \"Model Performance Summary\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 180,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Grid, {\n          container: true,\n          spacing: 3,\n          children: [/*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$r = bestModelMetrics.r2) === null || _bestModelMetrics$r === void 0 ? void 0 : _bestModelMetrics$r.toFixed(3)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 189,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"R\\xB2 Score\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 192,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: getR2Interpretation(bestModelMetrics.r2 || 0),\n                  size: \"small\",\n                  color: getR2Color(bestModelMetrics.r2 || 0),\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 195,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 188,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 187,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 186,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$rms = bestModelMetrics.rmse) === null || _bestModelMetrics$rms === void 0 ? void 0 : _bestModelMetrics$rms.toFixed(2)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 208,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"RMSE (minutes)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 211,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Root Mean Squared Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 214,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 207,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 206,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 205,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: ((_bestModelMetrics$mae = bestModelMetrics.mae) === null || _bestModelMetrics$mae === void 0 ? void 0 : _bestModelMetrics$mae.toFixed(2)) || 'N/A'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 227,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"MAE (minutes)\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 230,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Mean Absolute Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 233,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 226,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 225,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 224,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Grid, {\n            item: true,\n            xs: 12,\n            sm: 6,\n            md: 3,\n            children: /*#__PURE__*/_jsxDEV(Card, {\n              variant: \"outlined\",\n              children: /*#__PURE__*/_jsxDEV(CardContent, {\n                children: [/*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"h6\",\n                  color: \"primary\",\n                  gutterBottom: true,\n                  children: [((_bestModelMetrics$map = bestModelMetrics.mape) === null || _bestModelMetrics$map === void 0 ? void 0 : _bestModelMetrics$map.toFixed(1)) || 'N/A', \"%\"]\n                }, void 0, true, {\n                  fileName: _jsxFileName,\n                  lineNumber: 246,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Typography, {\n                  variant: \"body2\",\n                  color: \"text.secondary\",\n                  children: \"MAPE\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 249,\n                  columnNumber: 19\n                }, this), /*#__PURE__*/_jsxDEV(Chip, {\n                  label: \"Mean Absolute Percentage Error\",\n                  size: \"small\",\n                  variant: \"outlined\",\n                  sx: {\n                    mt: 1\n                  }\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 252,\n                  columnNumber: 19\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 245,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 244,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 243,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 185,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 263,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Model Evaluation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 265,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: getR2Color(bestModelMetrics.r2 || 0),\n          sx: {\n            mb: 2\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"subtitle2\",\n            gutterBottom: true,\n            children: [bestModel, \" - \", getR2Interpretation(bestModelMetrics.r2 || 0)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 273,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: bestModelMetrics.r2 >= 0.7 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows good predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r2 = bestModelMetrics.r2) === null || _bestModelMetrics$r2 === void 0 ? void 0 : _bestModelMetrics$r2.toFixed(3), \"indicates that \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model.\"]\n            }, void 0, true) : bestModelMetrics.r2 >= 0.5 ? /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows moderate predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r3 = bestModelMetrics.r2) === null || _bestModelMetrics$r3 === void 0 ? void 0 : _bestModelMetrics$r3.toFixed(3), \"indicates that only \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model. Consider adding more relevant features or collecting more data.\"]\n            }, void 0, true) : /*#__PURE__*/_jsxDEV(_Fragment, {\n              children: [\"This model shows poor predictive performance. The R\\xB2 score of \", (_bestModelMetrics$r4 = bestModelMetrics.r2) === null || _bestModelMetrics$r4 === void 0 ? void 0 : _bestModelMetrics$r4.toFixed(3), \"indicates that only \", (bestModelMetrics.r2 * 100).toFixed(1), \"% of the variance in the target variable is explained by the model. Consider:\", /*#__PURE__*/_jsxDEV(\"ul\", {\n                children: [/*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Adding more training data\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 296,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Engineering additional features\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 297,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Checking data quality\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 298,\n                  columnNumber: 21\n                }, this), /*#__PURE__*/_jsxDEV(\"li\", {\n                  children: \"Trying different algorithms\"\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 299,\n                  columnNumber: 21\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 295,\n                columnNumber: 19\n              }, this)]\n            }, void 0, true)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 277,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 269,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Practical Interpretation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 306,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 313,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 312,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `Average Error: ±${((_bestModelMetrics$mae2 = bestModelMetrics.mae) === null || _bestModelMetrics$mae2 === void 0 ? void 0 : _bestModelMetrics$mae2.toFixed(1)) || 'N/A'} minutes`,\n              secondary: \"On average, predictions will be off by this amount\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 315,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 311,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 323,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 322,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `Percentage Error: ${((_bestModelMetrics$map2 = bestModelMetrics.mape) === null || _bestModelMetrics$map2 === void 0 ? void 0 : _bestModelMetrics$map2.toFixed(1)) || 'N/A'}%`,\n              secondary: \"Relative error as a percentage of the actual value\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 325,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 321,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 310,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 179,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 178,\n      columnNumber: 7\n    }, this);\n  };\n  const renderFeatureImportance = () => {\n    if (!featureImportance) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"info\",\n        children: \"Loading feature importance data...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 339,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Parse feature importance data\n    const chartData = featureImportance.chart ? JSON.parse(featureImportance.chart) : null;\n    if (!chartData) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No feature importance data available.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 351,\n        columnNumber: 9\n      }, this);\n    }\n\n    // Extract top features from Plotly chart data\n    let topFeatures = [];\n    if (chartData.data && chartData.data.length > 0) {\n      const plotlyData = chartData.data[0];\n\n      // Handle horizontal bar chart format (x=importance, y=feature names)\n      if (plotlyData.x && plotlyData.y) {\n        topFeatures = plotlyData.y.map((featureName, index) => ({\n          name: featureName,\n          importance: plotlyData.x[index]\n        }));\n      }\n    }\n\n    // Fallback: try to extract from other possible data structures\n    if (topFeatures.length === 0 && chartData.data) {\n      topFeatures = chartData.data;\n    }\n\n    // If still no features, show a message\n    if (topFeatures.length === 0) {\n      return /*#__PURE__*/_jsxDEV(Alert, {\n        severity: \"warning\",\n        children: \"No feature importance data could be extracted from the model.\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 380,\n        columnNumber: 9\n      }, this);\n    }\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(BubbleChart, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 390,\n            columnNumber: 13\n          }, this), \"Feature Importance\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 389,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Feature importance shows which variables have the most influence on predictions. Higher values indicate stronger influence on the model's output.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 395,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 394,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: topFeatures.slice(0, 10).map((feature, index) => /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(TrendingUp, {\n                color: index < 3 ? 'primary' : 'action'\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 405,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 404,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: `${index + 1}. ${feature.name || feature.x || `Feature ${index + 1}`}`,\n              secondary: `Importance: ${(() => {\n                const importance = feature.importance || feature.y;\n                return typeof importance === 'number' ? importance.toFixed(3) : 'N/A';\n              })()}`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 407,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(Box, {\n              sx: {\n                width: '50%',\n                ml: 2\n              },\n              children: /*#__PURE__*/_jsxDEV(Box, {\n                sx: {\n                  height: 12,\n                  bgcolor: index < 3 ? 'primary.main' : 'primary.light',\n                  width: `${Math.min(100, Math.max(5, (typeof (feature.importance || feature.y) === 'number' ? feature.importance || feature.y : 0) * 100))}%`,\n                  minWidth: '5%',\n                  borderRadius: 1\n                }\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 415,\n                columnNumber: 19\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 414,\n              columnNumber: 17\n            }, this)]\n          }, index, true, {\n            fileName: _jsxFileName,\n            lineNumber: 403,\n            columnNumber: 15\n          }, this))\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 401,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 429,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Insights\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 431,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 438,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 437,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Top Predictors\",\n              secondary: `The top 3 features account for a significant portion of the model's predictive power.`\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 440,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 436,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 448,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 447,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Feature Selection\",\n              secondary: \"Consider focusing on the top features for simplified models or data collection.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 450,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 446,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 435,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 388,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 387,\n      columnNumber: 7\n    }, this);\n  };\n  const renderPredictionScatter = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(Timeline, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 466,\n            columnNumber: 13\n          }, this), \"Predicted vs Actual Values\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 465,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"This chart shows how well the model's predictions match the actual values. Points closer to the diagonal line indicate more accurate predictions.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 471,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 470,\n          columnNumber: 11\n        }, this), visualizations.predictionScatter ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 2\n          },\n          children: typeof visualizations.predictionScatter === 'string' ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/png;base64,${visualizations.predictionScatter}`,\n            alt: \"Prediction Scatter Plot\",\n            style: {\n              maxWidth: '100%',\n              height: 'auto'\n            },\n            onError: e => {\n              console.error('Failed to load prediction scatter image');\n              e.target.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 480,\n            columnNumber: 17\n          }, this) : visualizations.predictionScatter.type === 'plotly' ? /*#__PURE__*/_jsxDEV(Plot, {\n            data: JSON.parse(visualizations.predictionScatter.chart).data,\n            layout: {\n              ...JSON.parse(visualizations.predictionScatter.chart).layout,\n              autosize: true,\n              height: 400\n            },\n            config: {\n              responsive: true\n            },\n            style: {\n              width: '100%',\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 490,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"error\",\n            children: \"Invalid visualization format\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 501,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 478,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            children: activeTab === 1 ? 'Loading visualization...' : 'Click to load visualization'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 506,\n            columnNumber: 15\n          }, this), activeTab === 1 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => loadVisualization('predictionScatter'),\n            sx: {\n              mt: 2\n            },\n            children: \"Load Prediction Scatter Plot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 510,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 505,\n          columnNumber: 13\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 464,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 463,\n      columnNumber: 7\n    }, this);\n  };\n  const renderResidualsPlot = () => {\n    return /*#__PURE__*/_jsxDEV(Box, {\n      children: /*#__PURE__*/_jsxDEV(Paper, {\n        sx: {\n          p: 3,\n          mb: 3\n        },\n        children: [/*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"h6\",\n          gutterBottom: true,\n          children: [/*#__PURE__*/_jsxDEV(Timeline, {\n            sx: {\n              mr: 1,\n              verticalAlign: 'middle'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 530,\n            columnNumber: 13\n          }, this), \"Residuals Analysis\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 529,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Alert, {\n          severity: \"info\",\n          sx: {\n            mb: 3\n          },\n          children: /*#__PURE__*/_jsxDEV(Typography, {\n            variant: \"body2\",\n            children: \"Residuals are the differences between predicted and actual values. Ideally, residuals should be randomly distributed around zero with no pattern.\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 535,\n            columnNumber: 13\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 534,\n          columnNumber: 11\n        }, this), visualizations.residuals ? /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            mt: 2\n          },\n          children: typeof visualizations.residuals === 'string' ? /*#__PURE__*/_jsxDEV(\"img\", {\n            src: `data:image/png;base64,${visualizations.residuals}`,\n            alt: \"Residuals Plot\",\n            style: {\n              maxWidth: '100%',\n              height: 'auto'\n            },\n            onError: e => {\n              console.error('Failed to load residuals image');\n              e.target.style.display = 'none';\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 544,\n            columnNumber: 17\n          }, this) : visualizations.residuals.type === 'plotly' ? /*#__PURE__*/_jsxDEV(Plot, {\n            data: JSON.parse(visualizations.residuals.chart).data,\n            layout: {\n              ...JSON.parse(visualizations.residuals.chart).layout,\n              autosize: true,\n              height: 400\n            },\n            config: {\n              responsive: true\n            },\n            style: {\n              width: '100%',\n              height: '400px'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 554,\n            columnNumber: 17\n          }, this) : /*#__PURE__*/_jsxDEV(Typography, {\n            color: \"error\",\n            children: \"Invalid visualization format\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 565,\n            columnNumber: 17\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 542,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(Box, {\n          sx: {\n            textAlign: 'center',\n            p: 4\n          },\n          children: [/*#__PURE__*/_jsxDEV(Typography, {\n            color: \"text.secondary\",\n            children: activeTab === 3 ? 'Loading visualization...' : 'Click to load visualization'\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 570,\n            columnNumber: 15\n          }, this), activeTab === 3 && /*#__PURE__*/_jsxDEV(Button, {\n            variant: \"outlined\",\n            onClick: () => loadVisualization('residuals'),\n            sx: {\n              mt: 2\n            },\n            children: \"Load Residuals Plot\"\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 574,\n            columnNumber: 17\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 569,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(Divider, {\n          sx: {\n            my: 3\n          }\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 585,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Typography, {\n          variant: \"subtitle1\",\n          gutterBottom: true,\n          children: \"Interpretation\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 587,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(List, {\n          children: [/*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Info, {\n                color: \"primary\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 594,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 593,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Random Distribution\",\n              secondary: \"Residuals should be randomly scattered around zero with no clear pattern.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 596,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 592,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(ListItem, {\n            children: [/*#__PURE__*/_jsxDEV(ListItemIcon, {\n              children: /*#__PURE__*/_jsxDEV(Warning, {\n                color: \"warning\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 604,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 603,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(ListItemText, {\n              primary: \"Patterns to Watch For\",\n              secondary: \"Trends, curves, or clusters in residuals indicate the model may be missing important patterns.\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 606,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 602,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 591,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 528,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 527,\n      columnNumber: 7\n    }, this);\n  };\n  return /*#__PURE__*/_jsxDEV(Box, {\n    children: [/*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"h4\",\n      gutterBottom: true,\n      children: \"Model Evaluation\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 619,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Typography, {\n      variant: \"body1\",\n      color: \"text.secondary\",\n      paragraph: true,\n      children: \"Evaluate model performance and understand feature importance. This step helps you interpret the model's predictions and identify areas for improvement.\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 622,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(Paper, {\n      sx: {\n        mb: 3\n      },\n      children: /*#__PURE__*/_jsxDEV(Tabs, {\n        value: activeTab,\n        onChange: handleTabChange,\n        variant: \"fullWidth\",\n        indicatorColor: \"primary\",\n        textColor: \"primary\",\n        children: [/*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BarChart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 635,\n            columnNumber: 22\n          }, this),\n          label: \"Performance\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 635,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(Timeline, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 636,\n            columnNumber: 22\n          }, this),\n          label: \"Predictions\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 636,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(BubbleChart, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 637,\n            columnNumber: 22\n          }, this),\n          label: \"Features\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 637,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(Tab, {\n          icon: /*#__PURE__*/_jsxDEV(TrendingUp, {}, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 638,\n            columnNumber: 22\n          }, this),\n          label: \"Residuals\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 638,\n          columnNumber: 11\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 628,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 627,\n      columnNumber: 7\n    }, this), activeTab === 0 && renderPerformanceMetrics(), activeTab === 1 && renderPredictionScatter(), activeTab === 2 && renderFeatureImportance(), activeTab === 3 && renderResidualsPlot(), /*#__PURE__*/_jsxDEV(Box, {\n      sx: {\n        display: 'flex',\n        justifyContent: 'space-between',\n        mt: 4\n      },\n      children: [/*#__PURE__*/_jsxDEV(Button, {\n        variant: \"outlined\",\n        onClick: onBack,\n        startIcon: /*#__PURE__*/_jsxDEV(ArrowBack, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 651,\n          columnNumber: 22\n        }, this),\n        children: \"Back\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 648,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(Button, {\n        variant: \"contained\",\n        color: \"primary\",\n        onClick: onNext,\n        endIcon: /*#__PURE__*/_jsxDEV(ArrowForward, {}, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 660,\n          columnNumber: 20\n        }, this),\n        children: \"Continue to Predictions\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 656,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 647,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 618,\n    columnNumber: 5\n  }, this);\n};\n_s(ModelEvaluation, \"x8tyZii/MwAhoVwzm7YL36DZRd0=\");\n_c = ModelEvaluation;\nexport default ModelEvaluation;\nvar _c;\n$RefreshReg$(_c, \"ModelEvaluation\");", "map": {"version": 3, "names": ["React", "useState", "useEffect", "Plot", "Box", "Typography", "<PERSON><PERSON>", "Paper", "Grid", "Card", "<PERSON><PERSON><PERSON><PERSON>", "Divider", "Chip", "<PERSON><PERSON>", "List", "ListItem", "ListItemIcon", "ListItemText", "Tab", "Tabs", "ArrowBack", "ArrowForward", "<PERSON><PERSON><PERSON>", "Timeline", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "TrendingUp", "Info", "Warning", "CheckCircle", "Error", "toast", "apiService", "jsxDEV", "_jsxDEV", "Fragment", "_Fragment", "ModelEvaluation", "onNext", "onBack", "bestModel", "setLoading", "setError", "_s", "activeTab", "setActiveTab", "modelPerformance", "setModelPerformance", "featureImportance", "setFeatureImportance", "visualizations", "setVisualizations", "fetchModelPerformance", "fetchFeatureImportance", "result", "getModelPerformance", "error", "message", "getFeatureImportance", "handleTabChange", "_", "newValue", "predictionScatter", "loadVisualization", "residuals", "vizType", "generateVisualization", "image", "chart", "type", "prev", "console", "renderPerformanceMetrics", "_modelPerformance$das", "_bestModelMetrics$r", "_bestModelMetrics$rms", "_bestModelMetrics$mae", "_bestModelMetrics$map", "_bestModelMetrics$r2", "_bestModelMetrics$r3", "_bestModelMetrics$r4", "_bestModelMetrics$mae2", "_bestModelMetrics$map2", "severity", "children", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "metrics", "dashboard", "rmse_comparison", "JSON", "parse", "getR2Color", "r2", "getR2Interpretation", "bestModelMetrics", "best_model_metrics", "rmse", "mae", "mape", "sx", "p", "mb", "variant", "gutterBottom", "mr", "verticalAlign", "container", "spacing", "item", "xs", "sm", "md", "color", "toFixed", "label", "size", "mt", "my", "primary", "secondary", "renderFeatureImportance", "chartData", "topFeatures", "data", "length", "plotlyData", "x", "y", "map", "featureName", "index", "name", "importance", "slice", "feature", "width", "ml", "height", "bgcolor", "Math", "min", "max", "min<PERSON><PERSON><PERSON>", "borderRadius", "renderPredictionScatter", "textAlign", "src", "alt", "style", "max<PERSON><PERSON><PERSON>", "onError", "e", "target", "display", "layout", "autosize", "config", "responsive", "onClick", "renderResidualsPlot", "paragraph", "value", "onChange", "indicatorColor", "textColor", "icon", "justifyContent", "startIcon", "endIcon", "_c", "$RefreshReg$"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/src/components/ModelEvaluation.js"], "sourcesContent": ["import React, { useState, useEffect } from 'react';\r\nimport Plot from 'react-plotly.js';\r\nimport {\r\n  Box,\r\n  Typography,\r\n  Button,\r\n  Paper,\r\n  Grid,\r\n  Card,\r\n  CardContent,\r\n  Divider,\r\n  Chip,\r\n  Alert,\r\n  List,\r\n  ListItem,\r\n  ListItemIcon,\r\n  ListItemText,\r\n  Tab,\r\n  Tabs\r\n} from '@mui/material';\r\nimport {\r\n  ArrowBack,\r\n  ArrowForward,\r\n  BarChart,\r\n  Timeline,\r\n  BubbleChart,\r\n  TrendingUp,\r\n  Info,\r\n  Warning,\r\n  CheckCircle,\r\n  Error\r\n} from '@mui/icons-material';\r\nimport toast from 'react-hot-toast';\r\n\r\nimport { apiService } from '../services/apiService';\r\n\r\nconst ModelEvaluation = ({ onNext, onBack, bestModel, setLoading, setError }) => {\r\n  const [activeTab, setActiveTab] = useState(0);\r\n  const [modelPerformance, setModelPerformance] = useState(null);\r\n  const [featureImportance, setFeatureImportance] = useState(null);\r\n  const [visualizations, setVisualizations] = useState({});\r\n\r\n  useEffect(() => {\r\n    fetchModelPerformance();\r\n    fetchFeatureImportance();\r\n  }, [bestModel]);\r\n\r\n\r\n\r\n  const fetchModelPerformance = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const result = await apiService.getModelPerformance();\r\n      setModelPerformance(result);\r\n    } catch (error) {\r\n      setError('Failed to fetch model performance: ' + error.message);\r\n      toast.error('Failed to fetch model performance');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const fetchFeatureImportance = async () => {\r\n    setLoading(true);\r\n    try {\r\n      const result = await apiService.getFeatureImportance();\r\n      setFeatureImportance(result);\r\n    } catch (error) {\r\n      setError('Failed to fetch feature importance: ' + error.message);\r\n      toast.error('Failed to fetch feature importance');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const handleTabChange = (_, newValue) => {\r\n    setActiveTab(newValue);\r\n    \r\n    // Load visualization data for the selected tab if not already loaded\r\n    if (newValue === 1 && !visualizations.predictionScatter) {\r\n      loadVisualization('predictionScatter');\r\n    } else if (newValue === 2 && !visualizations.featureImportance) {\r\n      loadVisualization('featureImportance');\r\n    } else if (newValue === 3 && !visualizations.residuals) {\r\n      loadVisualization('residuals');\r\n    }\r\n  };\r\n\r\n  const loadVisualization = async (vizType) => {\r\n    setLoading(true);\r\n    try {\r\n      let result;\r\n\r\n      switch (vizType) {\r\n        case 'predictionScatter':\r\n          result = await apiService.generateVisualization('prediction_scatter');\r\n          // Handle both image and Plotly JSON responses\r\n          if (result.image) {\r\n            result = result.image;\r\n          } else if (result.chart) {\r\n            result = { type: 'plotly', chart: result.chart };\r\n          }\r\n          break;\r\n        case 'featureImportance':\r\n          result = await apiService.generateVisualization('feature_importance');\r\n          break;\r\n        case 'residuals':\r\n          result = await apiService.generateVisualization('residuals_plot');\r\n          // Handle both image and Plotly JSON responses\r\n          if (result.image) {\r\n            result = result.image;\r\n          } else if (result.chart) {\r\n            result = { type: 'plotly', chart: result.chart };\r\n          }\r\n          break;\r\n        default:\r\n          return;\r\n      }\r\n\r\n      setVisualizations(prev => ({\r\n        ...prev,\r\n        [vizType]: result\r\n      }));\r\n    } catch (error) {\r\n      console.error(`Failed to load ${vizType} visualization:`, error);\r\n      setError(`Failed to load ${vizType} visualization: ${error.message}`);\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const renderPerformanceMetrics = () => {\r\n    if (!modelPerformance) {\r\n      return (\r\n        <Alert severity=\"info\">\r\n          Loading model performance metrics...\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    const metrics = modelPerformance.dashboard?.rmse_comparison ? \r\n      JSON.parse(modelPerformance.dashboard.rmse_comparison) : null;\r\n    \r\n    if (!metrics) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No performance metrics available for the model.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Helper function to get color based on R² score\r\n    const getR2Color = (r2) => {\r\n      if (r2 >= 0.9) return 'success';\r\n      if (r2 >= 0.7) return 'primary';\r\n      if (r2 >= 0.5) return 'warning';\r\n      return 'error';\r\n    };\r\n    \r\n    // Helper function to get interpretation of R² score\r\n    const getR2Interpretation = (r2) => {\r\n      if (r2 >= 0.9) return 'Excellent fit';\r\n      if (r2 >= 0.7) return 'Good fit';\r\n      if (r2 >= 0.5) return 'Moderate fit';\r\n      if (r2 >= 0.0) return 'Poor fit';\r\n      return 'Very poor fit';\r\n    };\r\n\r\n    // Extract metrics for the best model\r\n    const bestModelMetrics = modelPerformance.best_model_metrics || {\r\n      r2: 0.7,\r\n      rmse: 45,\r\n      mae: 35,\r\n      mape: 15\r\n    };\r\n\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <BarChart sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Model Performance Summary\r\n          </Typography>\r\n          \r\n          <Grid container spacing={3}>\r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.r2?.toFixed(3) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    R² Score\r\n                  </Typography>\r\n                  <Chip \r\n                    label={getR2Interpretation(bestModelMetrics.r2 || 0)}\r\n                    size=\"small\"\r\n                    color={getR2Color(bestModelMetrics.r2 || 0)}\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.rmse?.toFixed(2) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    RMSE (minutes)\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Root Mean Squared Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.mae?.toFixed(2) || 'N/A'}\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    MAE (minutes)\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Mean Absolute Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n            \r\n            <Grid item xs={12} sm={6} md={3}>\r\n              <Card variant=\"outlined\">\r\n                <CardContent>\r\n                  <Typography variant=\"h6\" color=\"primary\" gutterBottom>\r\n                    {bestModelMetrics.mape?.toFixed(1) || 'N/A'}%\r\n                  </Typography>\r\n                  <Typography variant=\"body2\" color=\"text.secondary\">\r\n                    MAPE\r\n                  </Typography>\r\n                  <Chip \r\n                    label=\"Mean Absolute Percentage Error\"\r\n                    size=\"small\"\r\n                    variant=\"outlined\"\r\n                    sx={{ mt: 1 }}\r\n                  />\r\n                </CardContent>\r\n              </Card>\r\n            </Grid>\r\n          </Grid>\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Model Evaluation\r\n          </Typography>\r\n          \r\n          <Alert \r\n            severity={getR2Color(bestModelMetrics.r2 || 0)}\r\n            sx={{ mb: 2 }}\r\n          >\r\n            <Typography variant=\"subtitle2\" gutterBottom>\r\n              {bestModel} - {getR2Interpretation(bestModelMetrics.r2 || 0)}\r\n            </Typography>\r\n            \r\n            <Typography variant=\"body2\">\r\n              {bestModelMetrics.r2 >= 0.7 ? (\r\n                <>\r\n                  This model shows good predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model.\r\n                </>\r\n              ) : bestModelMetrics.r2 >= 0.5 ? (\r\n                <>\r\n                  This model shows moderate predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model. Consider adding more relevant features or collecting more data.\r\n                </>\r\n              ) : (\r\n                <>\r\n                  This model shows poor predictive performance. The R² score of {bestModelMetrics.r2?.toFixed(3)} \r\n                  indicates that only {(bestModelMetrics.r2 * 100).toFixed(1)}% of the variance in the target variable \r\n                  is explained by the model. Consider:\r\n                  <ul>\r\n                    <li>Adding more training data</li>\r\n                    <li>Engineering additional features</li>\r\n                    <li>Checking data quality</li>\r\n                    <li>Trying different algorithms</li>\r\n                  </ul>\r\n                </>\r\n              )}\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Practical Interpretation\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary={`Average Error: ±${bestModelMetrics.mae?.toFixed(1) || 'N/A'} minutes`}\r\n                secondary=\"On average, predictions will be off by this amount\"\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary={`Percentage Error: ${bestModelMetrics.mape?.toFixed(1) || 'N/A'}%`}\r\n                secondary=\"Relative error as a percentage of the actual value\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderFeatureImportance = () => {\r\n    if (!featureImportance) {\r\n      return (\r\n        <Alert severity=\"info\">\r\n          Loading feature importance data...\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Parse feature importance data\r\n    const chartData = featureImportance.chart ?\r\n      JSON.parse(featureImportance.chart) : null;\r\n\r\n    if (!chartData) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No feature importance data available.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    // Extract top features from Plotly chart data\r\n    let topFeatures = [];\r\n\r\n    if (chartData.data && chartData.data.length > 0) {\r\n      const plotlyData = chartData.data[0];\r\n\r\n      // Handle horizontal bar chart format (x=importance, y=feature names)\r\n      if (plotlyData.x && plotlyData.y) {\r\n        topFeatures = plotlyData.y.map((featureName, index) => ({\r\n          name: featureName,\r\n          importance: plotlyData.x[index]\r\n        }));\r\n      }\r\n    }\r\n\r\n    // Fallback: try to extract from other possible data structures\r\n    if (topFeatures.length === 0 && chartData.data) {\r\n      topFeatures = chartData.data;\r\n    }\r\n\r\n    // If still no features, show a message\r\n    if (topFeatures.length === 0) {\r\n      return (\r\n        <Alert severity=\"warning\">\r\n          No feature importance data could be extracted from the model.\r\n        </Alert>\r\n      );\r\n    }\r\n\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <BubbleChart sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Feature Importance\r\n          </Typography>\r\n\r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              Feature importance shows which variables have the most influence on predictions.\r\n              Higher values indicate stronger influence on the model's output.\r\n            </Typography>\r\n          </Alert>\r\n\r\n          <List>\r\n            {topFeatures.slice(0, 10).map((feature, index) => (\r\n              <ListItem key={index}>\r\n                <ListItemIcon>\r\n                  <TrendingUp color={index < 3 ? 'primary' : 'action'} />\r\n                </ListItemIcon>\r\n                <ListItemText\r\n                  primary={`${index + 1}. ${feature.name || feature.x || `Feature ${index + 1}`}`}\r\n                  secondary={`Importance: ${(() => {\r\n                    const importance = feature.importance || feature.y;\r\n                    return typeof importance === 'number' ? importance.toFixed(3) : 'N/A';\r\n                  })()}`}\r\n                />\r\n                <Box sx={{ width: '50%', ml: 2 }}>\r\n                  <Box\r\n                    sx={{\r\n                      height: 12,\r\n                      bgcolor: index < 3 ? 'primary.main' : 'primary.light',\r\n                      width: `${Math.min(100, Math.max(5, (typeof (feature.importance || feature.y) === 'number' ? (feature.importance || feature.y) : 0) * 100))}%`,\r\n                      minWidth: '5%',\r\n                      borderRadius: 1\r\n                    }}\r\n                  />\r\n                </Box>\r\n              </ListItem>\r\n            ))}\r\n          </List>\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Insights\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Top Predictors\"\r\n                secondary={`The top 3 features account for a significant portion of the model's predictive power.`}\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Feature Selection\"\r\n                secondary=\"Consider focusing on the top features for simplified models or data collection.\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderPredictionScatter = () => {\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Predicted vs Actual Values\r\n          </Typography>\r\n          \r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              This chart shows how well the model's predictions match the actual values.\r\n              Points closer to the diagonal line indicate more accurate predictions.\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          {visualizations.predictionScatter ? (\r\n            <Box sx={{ textAlign: 'center', mt: 2 }}>\r\n              {typeof visualizations.predictionScatter === 'string' ? (\r\n                <img\r\n                  src={`data:image/png;base64,${visualizations.predictionScatter}`}\r\n                  alt=\"Prediction Scatter Plot\"\r\n                  style={{ maxWidth: '100%', height: 'auto' }}\r\n                  onError={(e) => {\r\n                    console.error('Failed to load prediction scatter image');\r\n                    e.target.style.display = 'none';\r\n                  }}\r\n                />\r\n              ) : visualizations.predictionScatter.type === 'plotly' ? (\r\n                <Plot\r\n                  data={JSON.parse(visualizations.predictionScatter.chart).data}\r\n                  layout={{\r\n                    ...JSON.parse(visualizations.predictionScatter.chart).layout,\r\n                    autosize: true,\r\n                    height: 400\r\n                  }}\r\n                  config={{ responsive: true }}\r\n                  style={{ width: '100%', height: '400px' }}\r\n                />\r\n              ) : (\r\n                <Typography color=\"error\">Invalid visualization format</Typography>\r\n              )}\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ textAlign: 'center', p: 4 }}>\r\n              <Typography color=\"text.secondary\">\r\n                {activeTab === 1 ? 'Loading visualization...' : 'Click to load visualization'}\r\n              </Typography>\r\n              {activeTab === 1 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => loadVisualization('predictionScatter')}\r\n                  sx={{ mt: 2 }}\r\n                >\r\n                  Load Prediction Scatter Plot\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          )}\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  const renderResidualsPlot = () => {\r\n    return (\r\n      <Box>\r\n        <Paper sx={{ p: 3, mb: 3 }}>\r\n          <Typography variant=\"h6\" gutterBottom>\r\n            <Timeline sx={{ mr: 1, verticalAlign: 'middle' }} />\r\n            Residuals Analysis\r\n          </Typography>\r\n          \r\n          <Alert severity=\"info\" sx={{ mb: 3 }}>\r\n            <Typography variant=\"body2\">\r\n              Residuals are the differences between predicted and actual values.\r\n              Ideally, residuals should be randomly distributed around zero with no pattern.\r\n            </Typography>\r\n          </Alert>\r\n          \r\n          {visualizations.residuals ? (\r\n            <Box sx={{ textAlign: 'center', mt: 2 }}>\r\n              {typeof visualizations.residuals === 'string' ? (\r\n                <img\r\n                  src={`data:image/png;base64,${visualizations.residuals}`}\r\n                  alt=\"Residuals Plot\"\r\n                  style={{ maxWidth: '100%', height: 'auto' }}\r\n                  onError={(e) => {\r\n                    console.error('Failed to load residuals image');\r\n                    e.target.style.display = 'none';\r\n                  }}\r\n                />\r\n              ) : visualizations.residuals.type === 'plotly' ? (\r\n                <Plot\r\n                  data={JSON.parse(visualizations.residuals.chart).data}\r\n                  layout={{\r\n                    ...JSON.parse(visualizations.residuals.chart).layout,\r\n                    autosize: true,\r\n                    height: 400\r\n                  }}\r\n                  config={{ responsive: true }}\r\n                  style={{ width: '100%', height: '400px' }}\r\n                />\r\n              ) : (\r\n                <Typography color=\"error\">Invalid visualization format</Typography>\r\n              )}\r\n            </Box>\r\n          ) : (\r\n            <Box sx={{ textAlign: 'center', p: 4 }}>\r\n              <Typography color=\"text.secondary\">\r\n                {activeTab === 3 ? 'Loading visualization...' : 'Click to load visualization'}\r\n              </Typography>\r\n              {activeTab === 3 && (\r\n                <Button\r\n                  variant=\"outlined\"\r\n                  onClick={() => loadVisualization('residuals')}\r\n                  sx={{ mt: 2 }}\r\n                >\r\n                  Load Residuals Plot\r\n                </Button>\r\n              )}\r\n            </Box>\r\n          )}\r\n          \r\n          <Divider sx={{ my: 3 }} />\r\n          \r\n          <Typography variant=\"subtitle1\" gutterBottom>\r\n            Interpretation\r\n          </Typography>\r\n          \r\n          <List>\r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Info color=\"primary\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Random Distribution\"\r\n                secondary=\"Residuals should be randomly scattered around zero with no clear pattern.\"\r\n              />\r\n            </ListItem>\r\n            \r\n            <ListItem>\r\n              <ListItemIcon>\r\n                <Warning color=\"warning\" />\r\n              </ListItemIcon>\r\n              <ListItemText \r\n                primary=\"Patterns to Watch For\"\r\n                secondary=\"Trends, curves, or clusters in residuals indicate the model may be missing important patterns.\"\r\n              />\r\n            </ListItem>\r\n          </List>\r\n        </Paper>\r\n      </Box>\r\n    );\r\n  };\r\n\r\n  return (\r\n    <Box>\r\n      <Typography variant=\"h4\" gutterBottom>\r\n        Model Evaluation\r\n      </Typography>\r\n      <Typography variant=\"body1\" color=\"text.secondary\" paragraph>\r\n        Evaluate model performance and understand feature importance.\r\n        This step helps you interpret the model's predictions and identify areas for improvement.\r\n      </Typography>\r\n      \r\n      <Paper sx={{ mb: 3 }}>\r\n        <Tabs\r\n          value={activeTab}\r\n          onChange={handleTabChange}\r\n          variant=\"fullWidth\"\r\n          indicatorColor=\"primary\"\r\n          textColor=\"primary\"\r\n        >\r\n          <Tab icon={<BarChart />} label=\"Performance\" />\r\n          <Tab icon={<Timeline />} label=\"Predictions\" />\r\n          <Tab icon={<BubbleChart />} label=\"Features\" />\r\n          <Tab icon={<TrendingUp />} label=\"Residuals\" />\r\n        </Tabs>\r\n      </Paper>\r\n      \r\n      {activeTab === 0 && renderPerformanceMetrics()}\r\n      {activeTab === 1 && renderPredictionScatter()}\r\n      {activeTab === 2 && renderFeatureImportance()}\r\n      {activeTab === 3 && renderResidualsPlot()}\r\n      \r\n      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>\r\n        <Button \r\n          variant=\"outlined\" \r\n          onClick={onBack}\r\n          startIcon={<ArrowBack />}\r\n        >\r\n          Back\r\n        </Button>\r\n        \r\n        <Button \r\n          variant=\"contained\" \r\n          color=\"primary\" \r\n          onClick={onNext}\r\n          endIcon={<ArrowForward />}\r\n        >\r\n          Continue to Predictions\r\n        </Button>\r\n      </Box>\r\n    </Box>\r\n  );\r\n};\r\n\r\nexport default ModelEvaluation;"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,SAAS,QAAQ,OAAO;AAClD,OAAOC,IAAI,MAAM,iBAAiB;AAClC,SACEC,GAAG,EACHC,UAAU,EACVC,MAAM,EACNC,KAAK,EACLC,IAAI,EACJC,IAAI,EACJC,WAAW,EACXC,OAAO,EACPC,IAAI,EACJC,KAAK,EACLC,IAAI,EACJC,QAAQ,EACRC,YAAY,EACZC,YAAY,EACZC,GAAG,EACHC,IAAI,QACC,eAAe;AACtB,SACEC,SAAS,EACTC,YAAY,EACZC,QAAQ,EACRC,QAAQ,EACRC,WAAW,EACXC,UAAU,EACVC,IAAI,EACJC,OAAO,EACPC,WAAW,EACXC,KAAK,QACA,qBAAqB;AAC5B,OAAOC,KAAK,MAAM,iBAAiB;AAEnC,SAASC,UAAU,QAAQ,wBAAwB;AAAC,SAAAC,MAAA,IAAAC,OAAA,EAAAC,QAAA,IAAAC,SAAA;AAEpD,MAAMC,eAAe,GAAGA,CAAC;EAAEC,MAAM;EAAEC,MAAM;EAAEC,SAAS;EAAEC,UAAU;EAAEC;AAAS,CAAC,KAAK;EAAAC,EAAA;EAC/E,MAAM,CAACC,SAAS,EAAEC,YAAY,CAAC,GAAG3C,QAAQ,CAAC,CAAC,CAAC;EAC7C,MAAM,CAAC4C,gBAAgB,EAAEC,mBAAmB,CAAC,GAAG7C,QAAQ,CAAC,IAAI,CAAC;EAC9D,MAAM,CAAC8C,iBAAiB,EAAEC,oBAAoB,CAAC,GAAG/C,QAAQ,CAAC,IAAI,CAAC;EAChE,MAAM,CAACgD,cAAc,EAAEC,iBAAiB,CAAC,GAAGjD,QAAQ,CAAC,CAAC,CAAC,CAAC;EAExDC,SAAS,CAAC,MAAM;IACdiD,qBAAqB,CAAC,CAAC;IACvBC,sBAAsB,CAAC,CAAC;EAC1B,CAAC,EAAE,CAACb,SAAS,CAAC,CAAC;EAIf,MAAMY,qBAAqB,GAAG,MAAAA,CAAA,KAAY;IACxCX,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMa,MAAM,GAAG,MAAMtB,UAAU,CAACuB,mBAAmB,CAAC,CAAC;MACrDR,mBAAmB,CAACO,MAAM,CAAC;IAC7B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdd,QAAQ,CAAC,qCAAqC,GAAGc,KAAK,CAACC,OAAO,CAAC;MAC/D1B,KAAK,CAACyB,KAAK,CAAC,mCAAmC,CAAC;IAClD,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMY,sBAAsB,GAAG,MAAAA,CAAA,KAAY;IACzCZ,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,MAAMa,MAAM,GAAG,MAAMtB,UAAU,CAAC0B,oBAAoB,CAAC,CAAC;MACtDT,oBAAoB,CAACK,MAAM,CAAC;IAC9B,CAAC,CAAC,OAAOE,KAAK,EAAE;MACdd,QAAQ,CAAC,sCAAsC,GAAGc,KAAK,CAACC,OAAO,CAAC;MAChE1B,KAAK,CAACyB,KAAK,CAAC,oCAAoC,CAAC;IACnD,CAAC,SAAS;MACRf,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAMkB,eAAe,GAAGA,CAACC,CAAC,EAAEC,QAAQ,KAAK;IACvChB,YAAY,CAACgB,QAAQ,CAAC;;IAEtB;IACA,IAAIA,QAAQ,KAAK,CAAC,IAAI,CAACX,cAAc,CAACY,iBAAiB,EAAE;MACvDC,iBAAiB,CAAC,mBAAmB,CAAC;IACxC,CAAC,MAAM,IAAIF,QAAQ,KAAK,CAAC,IAAI,CAACX,cAAc,CAACF,iBAAiB,EAAE;MAC9De,iBAAiB,CAAC,mBAAmB,CAAC;IACxC,CAAC,MAAM,IAAIF,QAAQ,KAAK,CAAC,IAAI,CAACX,cAAc,CAACc,SAAS,EAAE;MACtDD,iBAAiB,CAAC,WAAW,CAAC;IAChC;EACF,CAAC;EAED,MAAMA,iBAAiB,GAAG,MAAOE,OAAO,IAAK;IAC3CxB,UAAU,CAAC,IAAI,CAAC;IAChB,IAAI;MACF,IAAIa,MAAM;MAEV,QAAQW,OAAO;QACb,KAAK,mBAAmB;UACtBX,MAAM,GAAG,MAAMtB,UAAU,CAACkC,qBAAqB,CAAC,oBAAoB,CAAC;UACrE;UACA,IAAIZ,MAAM,CAACa,KAAK,EAAE;YAChBb,MAAM,GAAGA,MAAM,CAACa,KAAK;UACvB,CAAC,MAAM,IAAIb,MAAM,CAACc,KAAK,EAAE;YACvBd,MAAM,GAAG;cAAEe,IAAI,EAAE,QAAQ;cAAED,KAAK,EAAEd,MAAM,CAACc;YAAM,CAAC;UAClD;UACA;QACF,KAAK,mBAAmB;UACtBd,MAAM,GAAG,MAAMtB,UAAU,CAACkC,qBAAqB,CAAC,oBAAoB,CAAC;UACrE;QACF,KAAK,WAAW;UACdZ,MAAM,GAAG,MAAMtB,UAAU,CAACkC,qBAAqB,CAAC,gBAAgB,CAAC;UACjE;UACA,IAAIZ,MAAM,CAACa,KAAK,EAAE;YAChBb,MAAM,GAAGA,MAAM,CAACa,KAAK;UACvB,CAAC,MAAM,IAAIb,MAAM,CAACc,KAAK,EAAE;YACvBd,MAAM,GAAG;cAAEe,IAAI,EAAE,QAAQ;cAAED,KAAK,EAAEd,MAAM,CAACc;YAAM,CAAC;UAClD;UACA;QACF;UACE;MACJ;MAEAjB,iBAAiB,CAACmB,IAAI,KAAK;QACzB,GAAGA,IAAI;QACP,CAACL,OAAO,GAAGX;MACb,CAAC,CAAC,CAAC;IACL,CAAC,CAAC,OAAOE,KAAK,EAAE;MACde,OAAO,CAACf,KAAK,CAAC,kBAAkBS,OAAO,iBAAiB,EAAET,KAAK,CAAC;MAChEd,QAAQ,CAAC,kBAAkBuB,OAAO,mBAAmBT,KAAK,CAACC,OAAO,EAAE,CAAC;IACvE,CAAC,SAAS;MACRhB,UAAU,CAAC,KAAK,CAAC;IACnB;EACF,CAAC;EAED,MAAM+B,wBAAwB,GAAGA,CAAA,KAAM;IAAA,IAAAC,qBAAA,EAAAC,mBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,oBAAA,EAAAC,sBAAA,EAAAC,sBAAA;IACrC,IAAI,CAACpC,gBAAgB,EAAE;MACrB,oBACEZ,OAAA,CAACpB,KAAK;QAACqE,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,MAAMC,OAAO,GAAG,CAAAhB,qBAAA,GAAA3B,gBAAgB,CAAC4C,SAAS,cAAAjB,qBAAA,eAA1BA,qBAAA,CAA4BkB,eAAe,GACzDC,IAAI,CAACC,KAAK,CAAC/C,gBAAgB,CAAC4C,SAAS,CAACC,eAAe,CAAC,GAAG,IAAI;IAE/D,IAAI,CAACF,OAAO,EAAE;MACZ,oBACEvD,OAAA,CAACpB,KAAK;QAACqE,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,MAAMM,UAAU,GAAIC,EAAE,IAAK;MACzB,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,SAAS;MAC/B,OAAO,OAAO;IAChB,CAAC;;IAED;IACA,MAAMC,mBAAmB,GAAID,EAAE,IAAK;MAClC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,eAAe;MACrC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,UAAU;MAChC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,cAAc;MACpC,IAAIA,EAAE,IAAI,GAAG,EAAE,OAAO,UAAU;MAChC,OAAO,eAAe;IACxB,CAAC;;IAED;IACA,MAAME,gBAAgB,GAAGnD,gBAAgB,CAACoD,kBAAkB,IAAI;MAC9DH,EAAE,EAAE,GAAG;MACPI,IAAI,EAAE,EAAE;MACRC,GAAG,EAAE,EAAE;MACPC,IAAI,EAAE;IACR,CAAC;IAED,oBACEnE,OAAA,CAAC7B,GAAG;MAAA+E,QAAA,eACFlD,OAAA,CAAC1B,KAAK;QAAC8F,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBlD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnClD,OAAA,CAACX,QAAQ;YAAC+E,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,6BAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACzB,IAAI;UAACoG,SAAS;UAACC,OAAO,EAAE,CAAE;UAAA1B,QAAA,gBACzBlD,OAAA,CAACzB,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BlD,OAAA,CAACxB,IAAI;cAAC+F,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBlD,OAAA,CAACvB,WAAW;gBAAAyE,QAAA,gBACVlD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,EAClD,EAAAV,mBAAA,GAAAuB,gBAAgB,CAACF,EAAE,cAAArB,mBAAA,uBAAnBA,mBAAA,CAAqB0C,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC/B,CAAC,eACbtD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,IAAI;kBACHwG,KAAK,EAAErB,mBAAmB,CAACC,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;kBACrDuB,IAAI,EAAC,OAAO;kBACZH,KAAK,EAAErB,UAAU,CAACG,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;kBAC5CO,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPtD,OAAA,CAACzB,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BlD,OAAA,CAACxB,IAAI;cAAC+F,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBlD,OAAA,CAACvB,WAAW;gBAAAyE,QAAA,gBACVlD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,EAClD,EAAAT,qBAAA,GAAAsB,gBAAgB,CAACE,IAAI,cAAAxB,qBAAA,uBAArBA,qBAAA,CAAuByC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACjC,CAAC,eACbtD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,IAAI;kBACHwG,KAAK,EAAC,yBAAyB;kBAC/BC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPtD,OAAA,CAACzB,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BlD,OAAA,CAACxB,IAAI;cAAC+F,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBlD,OAAA,CAACvB,WAAW;gBAAAyE,QAAA,gBACVlD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,EAClD,EAAAR,qBAAA,GAAAqB,gBAAgB,CAACG,GAAG,cAAAxB,qBAAA,uBAApBA,qBAAA,CAAsBwC,OAAO,CAAC,CAAC,CAAC,KAAI;gBAAK;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAChC,CAAC,eACbtD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,IAAI;kBACHwG,KAAK,EAAC,qBAAqB;kBAC3BC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,eAEPtD,OAAA,CAACzB,IAAI;YAACsG,IAAI;YAACC,EAAE,EAAE,EAAG;YAACC,EAAE,EAAE,CAAE;YAACC,EAAE,EAAE,CAAE;YAAA9B,QAAA,eAC9BlD,OAAA,CAACxB,IAAI;cAAC+F,OAAO,EAAC,UAAU;cAAArB,QAAA,eACtBlD,OAAA,CAACvB,WAAW;gBAAAyE,QAAA,gBACVlD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,IAAI;kBAACU,KAAK,EAAC,SAAS;kBAACT,YAAY;kBAAAtB,QAAA,GAClD,EAAAP,qBAAA,GAAAoB,gBAAgB,CAACI,IAAI,cAAAxB,qBAAA,uBAArBA,qBAAA,CAAuBuC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,EAAC,GAC9C;gBAAA;kBAAA/B,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbtD,OAAA,CAAC5B,UAAU;kBAACmG,OAAO,EAAC,OAAO;kBAACU,KAAK,EAAC,gBAAgB;kBAAA/B,QAAA,EAAC;gBAEnD;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAY,CAAC,eACbtD,OAAA,CAACrB,IAAI;kBACHwG,KAAK,EAAC,gCAAgC;kBACtCC,IAAI,EAAC,OAAO;kBACZb,OAAO,EAAC,UAAU;kBAClBH,EAAE,EAAE;oBAAEiB,EAAE,EAAE;kBAAE;gBAAE;kBAAAlC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OACf,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACS;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACV;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,eAEPtD,OAAA,CAACtB,OAAO;UAAC0F,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BtD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACpB,KAAK;UACJqE,QAAQ,EAAEW,UAAU,CAACG,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAE;UAC/CO,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,gBAEdlD,OAAA,CAAC5B,UAAU;YAACmG,OAAO,EAAC,WAAW;YAACC,YAAY;YAAAtB,QAAA,GACzC5C,SAAS,EAAC,KAAG,EAACwD,mBAAmB,CAACC,gBAAgB,CAACF,EAAE,IAAI,CAAC,CAAC;UAAA;YAAAV,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAClD,CAAC,eAEbtD,OAAA,CAAC5B,UAAU;YAACmG,OAAO,EAAC,OAAO;YAAArB,QAAA,EACxBa,gBAAgB,CAACF,EAAE,IAAI,GAAG,gBACzB7D,OAAA,CAAAE,SAAA;cAAAgD,QAAA,GAAE,mEAC8D,GAAAN,oBAAA,GAACmB,gBAAgB,CAACF,EAAE,cAAAjB,oBAAA,uBAAnBA,oBAAA,CAAqBsC,OAAO,CAAC,CAAC,CAAC,EAAC,iBAChF,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,qEAEzD;YAAA,eAAE,CAAC,GACDnB,gBAAgB,CAACF,EAAE,IAAI,GAAG,gBAC5B7D,OAAA,CAAAE,SAAA;cAAAgD,QAAA,GAAE,uEACkE,GAAAL,oBAAA,GAACkB,gBAAgB,CAACF,EAAE,cAAAhB,oBAAA,uBAAnBA,oBAAA,CAAqBqC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAC/E,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,qIAE9D;YAAA,eAAE,CAAC,gBAEHlF,OAAA,CAAAE,SAAA;cAAAgD,QAAA,GAAE,mEAC8D,GAAAJ,oBAAA,GAACiB,gBAAgB,CAACF,EAAE,cAAAf,oBAAA,uBAAnBA,oBAAA,CAAqBoC,OAAO,CAAC,CAAC,CAAC,EAAC,sBAC3E,EAAC,CAACnB,gBAAgB,CAACF,EAAE,GAAG,GAAG,EAAEqB,OAAO,CAAC,CAAC,CAAC,EAAC,+EAE5D,eAAAlF,OAAA;gBAAAkD,QAAA,gBACElD,OAAA;kBAAAkD,QAAA,EAAI;gBAAyB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAClCtD,OAAA;kBAAAkD,QAAA,EAAI;gBAA+B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eACxCtD,OAAA;kBAAAkD,QAAA,EAAI;gBAAqB;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC,eAC9BtD,OAAA;kBAAAkD,QAAA,EAAI;gBAA2B;kBAAAC,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAAI,CAAC;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAClC,CAAC;YAAA,eACL;UACH;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACS,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAERtD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACnB,IAAI;UAAAqE,QAAA,gBACHlD,OAAA,CAAClB,QAAQ;YAAAoE,QAAA,gBACPlD,OAAA,CAACjB,YAAY;cAAAmE,QAAA,eACXlD,OAAA,CAACP,IAAI;gBAACwF,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACftD,OAAA,CAAChB,YAAY;cACXuG,OAAO,EAAE,mBAAmB,EAAAxC,sBAAA,GAAAgB,gBAAgB,CAACG,GAAG,cAAAnB,sBAAA,uBAApBA,sBAAA,CAAsBmC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,UAAW;cAChFM,SAAS,EAAC;YAAoD;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXtD,OAAA,CAAClB,QAAQ;YAAAoE,QAAA,gBACPlD,OAAA,CAACjB,YAAY;cAAAmE,QAAA,eACXlD,OAAA,CAACP,IAAI;gBAACwF,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACftD,OAAA,CAAChB,YAAY;cACXuG,OAAO,EAAE,qBAAqB,EAAAvC,sBAAA,GAAAe,gBAAgB,CAACI,IAAI,cAAAnB,sBAAA,uBAArBA,sBAAA,CAAuBkC,OAAO,CAAC,CAAC,CAAC,KAAI,KAAK,GAAI;cAC5EM,SAAS,EAAC;YAAoD;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC/D,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAMmC,uBAAuB,GAAGA,CAAA,KAAM;IACpC,IAAI,CAAC3E,iBAAiB,EAAE;MACtB,oBACEd,OAAA,CAACpB,KAAK;QAACqE,QAAQ,EAAC,MAAM;QAAAC,QAAA,EAAC;MAEvB;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,MAAMoC,SAAS,GAAG5E,iBAAiB,CAACoB,KAAK,GACvCwB,IAAI,CAACC,KAAK,CAAC7C,iBAAiB,CAACoB,KAAK,CAAC,GAAG,IAAI;IAE5C,IAAI,CAACwD,SAAS,EAAE;MACd,oBACE1F,OAAA,CAACpB,KAAK;QAACqE,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;;IAEA;IACA,IAAIqC,WAAW,GAAG,EAAE;IAEpB,IAAID,SAAS,CAACE,IAAI,IAAIF,SAAS,CAACE,IAAI,CAACC,MAAM,GAAG,CAAC,EAAE;MAC/C,MAAMC,UAAU,GAAGJ,SAAS,CAACE,IAAI,CAAC,CAAC,CAAC;;MAEpC;MACA,IAAIE,UAAU,CAACC,CAAC,IAAID,UAAU,CAACE,CAAC,EAAE;QAChCL,WAAW,GAAGG,UAAU,CAACE,CAAC,CAACC,GAAG,CAAC,CAACC,WAAW,EAAEC,KAAK,MAAM;UACtDC,IAAI,EAAEF,WAAW;UACjBG,UAAU,EAAEP,UAAU,CAACC,CAAC,CAACI,KAAK;QAChC,CAAC,CAAC,CAAC;MACL;IACF;;IAEA;IACA,IAAIR,WAAW,CAACE,MAAM,KAAK,CAAC,IAAIH,SAAS,CAACE,IAAI,EAAE;MAC9CD,WAAW,GAAGD,SAAS,CAACE,IAAI;IAC9B;;IAEA;IACA,IAAID,WAAW,CAACE,MAAM,KAAK,CAAC,EAAE;MAC5B,oBACE7F,OAAA,CAACpB,KAAK;QAACqE,QAAQ,EAAC,SAAS;QAAAC,QAAA,EAAC;MAE1B;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAO,CAAC;IAEZ;IAEA,oBACEtD,OAAA,CAAC7B,GAAG;MAAA+E,QAAA,eACFlD,OAAA,CAAC1B,KAAK;QAAC8F,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBlD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnClD,OAAA,CAACT,WAAW;YAAC6E,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEzD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACpB,KAAK;UAACqE,QAAQ,EAAC,MAAM;UAACmB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACnClD,OAAA,CAAC5B,UAAU;YAACmG,OAAO,EAAC,OAAO;YAAArB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,eAERtD,OAAA,CAACnB,IAAI;UAAAqE,QAAA,EACFyC,WAAW,CAACW,KAAK,CAAC,CAAC,EAAE,EAAE,CAAC,CAACL,GAAG,CAAC,CAACM,OAAO,EAAEJ,KAAK,kBAC3CnG,OAAA,CAAClB,QAAQ;YAAAoE,QAAA,gBACPlD,OAAA,CAACjB,YAAY;cAAAmE,QAAA,eACXlD,OAAA,CAACR,UAAU;gBAACyF,KAAK,EAAEkB,KAAK,GAAG,CAAC,GAAG,SAAS,GAAG;cAAS;gBAAAhD,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3C,CAAC,eACftD,OAAA,CAAChB,YAAY;cACXuG,OAAO,EAAE,GAAGY,KAAK,GAAG,CAAC,KAAKI,OAAO,CAACH,IAAI,IAAIG,OAAO,CAACR,CAAC,IAAI,WAAWI,KAAK,GAAG,CAAC,EAAE,EAAG;cAChFX,SAAS,EAAE,eAAe,CAAC,MAAM;gBAC/B,MAAMa,UAAU,GAAGE,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACP,CAAC;gBAClD,OAAO,OAAOK,UAAU,KAAK,QAAQ,GAAGA,UAAU,CAACnB,OAAO,CAAC,CAAC,CAAC,GAAG,KAAK;cACvE,CAAC,EAAE,CAAC;YAAG;cAAA/B,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACR,CAAC,eACFtD,OAAA,CAAC7B,GAAG;cAACiG,EAAE,EAAE;gBAAEoC,KAAK,EAAE,KAAK;gBAAEC,EAAE,EAAE;cAAE,CAAE;cAAAvD,QAAA,eAC/BlD,OAAA,CAAC7B,GAAG;gBACFiG,EAAE,EAAE;kBACFsC,MAAM,EAAE,EAAE;kBACVC,OAAO,EAAER,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,eAAe;kBACrDK,KAAK,EAAE,GAAGI,IAAI,CAACC,GAAG,CAAC,GAAG,EAAED,IAAI,CAACE,GAAG,CAAC,CAAC,EAAE,CAAC,QAAQP,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACP,CAAC,CAAC,KAAK,QAAQ,GAAIO,OAAO,CAACF,UAAU,IAAIE,OAAO,CAACP,CAAC,GAAI,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG;kBAC9Ie,QAAQ,EAAE,IAAI;kBACdC,YAAY,EAAE;gBAChB;cAAE;gBAAA7D,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACH;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACC,CAAC;UAAA,GArBO6C,KAAK;YAAAhD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAsBV,CACX;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,eAEPtD,OAAA,CAACtB,OAAO;UAAC0F,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BtD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACnB,IAAI;UAAAqE,QAAA,gBACHlD,OAAA,CAAClB,QAAQ;YAAAoE,QAAA,gBACPlD,OAAA,CAACjB,YAAY;cAAAmE,QAAA,eACXlD,OAAA,CAACP,IAAI;gBAACwF,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACftD,OAAA,CAAChB,YAAY;cACXuG,OAAO,EAAC,gBAAgB;cACxBC,SAAS,EAAE;YAAwF;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACpG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXtD,OAAA,CAAClB,QAAQ;YAAAoE,QAAA,gBACPlD,OAAA,CAACjB,YAAY;cAAAmE,QAAA,eACXlD,OAAA,CAACP,IAAI;gBAACwF,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACftD,OAAA,CAAChB,YAAY;cACXuG,OAAO,EAAC,mBAAmB;cAC3BC,SAAS,EAAC;YAAiF;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC5F,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAM2D,uBAAuB,GAAGA,CAAA,KAAM;IACpC,oBACEjH,OAAA,CAAC7B,GAAG;MAAA+E,QAAA,eACFlD,OAAA,CAAC1B,KAAK;QAAC8F,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBlD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnClD,OAAA,CAACV,QAAQ;YAAC8E,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,8BAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACpB,KAAK;UAACqE,QAAQ,EAAC,MAAM;UAACmB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACnClD,OAAA,CAAC5B,UAAU;YAACmG,OAAO,EAAC,OAAO;YAAArB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEPtC,cAAc,CAACY,iBAAiB,gBAC/B5B,OAAA,CAAC7B,GAAG;UAACiG,EAAE,EAAE;YAAE8C,SAAS,EAAE,QAAQ;YAAE7B,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,EACrC,OAAOlC,cAAc,CAACY,iBAAiB,KAAK,QAAQ,gBACnD5B,OAAA;YACEmH,GAAG,EAAE,yBAAyBnG,cAAc,CAACY,iBAAiB,EAAG;YACjEwF,GAAG,EAAC,yBAAyB;YAC7BC,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEZ,MAAM,EAAE;YAAO,CAAE;YAC5Ca,OAAO,EAAGC,CAAC,IAAK;cACdnF,OAAO,CAACf,KAAK,CAAC,yCAAyC,CAAC;cACxDkG,CAAC,CAACC,MAAM,CAACJ,KAAK,CAACK,OAAO,GAAG,MAAM;YACjC;UAAE;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACAtC,cAAc,CAACY,iBAAiB,CAACO,IAAI,KAAK,QAAQ,gBACpDnC,OAAA,CAAC9B,IAAI;YACH0H,IAAI,EAAElC,IAAI,CAACC,KAAK,CAAC3C,cAAc,CAACY,iBAAiB,CAACM,KAAK,CAAC,CAAC0D,IAAK;YAC9D+B,MAAM,EAAE;cACN,GAAGjE,IAAI,CAACC,KAAK,CAAC3C,cAAc,CAACY,iBAAiB,CAACM,KAAK,CAAC,CAACyF,MAAM;cAC5DC,QAAQ,EAAE,IAAI;cACdlB,MAAM,EAAE;YACV,CAAE;YACFmB,MAAM,EAAE;cAAEC,UAAU,EAAE;YAAK,CAAE;YAC7BT,KAAK,EAAE;cAAEb,KAAK,EAAE,MAAM;cAAEE,MAAM,EAAE;YAAQ;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAEFtD,OAAA,CAAC5B,UAAU;YAAC6G,KAAK,EAAC,OAAO;YAAA/B,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENtD,OAAA,CAAC7B,GAAG;UAACiG,EAAE,EAAE;YAAE8C,SAAS,EAAE,QAAQ;YAAE7C,CAAC,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACrClD,OAAA,CAAC5B,UAAU;YAAC6G,KAAK,EAAC,gBAAgB;YAAA/B,QAAA,EAC/BxC,SAAS,KAAK,CAAC,GAAG,0BAA0B,GAAG;UAA6B;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,EACZ5C,SAAS,KAAK,CAAC,iBACdV,OAAA,CAAC3B,MAAM;YACLkG,OAAO,EAAC,UAAU;YAClBwD,OAAO,EAAEA,CAAA,KAAMlG,iBAAiB,CAAC,mBAAmB,CAAE;YACtDuC,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,EACf;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACI;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,MAAM0E,mBAAmB,GAAGA,CAAA,KAAM;IAChC,oBACEhI,OAAA,CAAC7B,GAAG;MAAA+E,QAAA,eACFlD,OAAA,CAAC1B,KAAK;QAAC8F,EAAE,EAAE;UAAEC,CAAC,EAAE,CAAC;UAAEC,EAAE,EAAE;QAAE,CAAE;QAAApB,QAAA,gBACzBlD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,IAAI;UAACC,YAAY;UAAAtB,QAAA,gBACnClD,OAAA,CAACV,QAAQ;YAAC8E,EAAE,EAAE;cAAEK,EAAE,EAAE,CAAC;cAAEC,aAAa,EAAE;YAAS;UAAE;YAAAvB,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC,sBAEtD;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACpB,KAAK;UAACqE,QAAQ,EAAC,MAAM;UAACmB,EAAE,EAAE;YAAEE,EAAE,EAAE;UAAE,CAAE;UAAApB,QAAA,eACnClD,OAAA,CAAC5B,UAAU;YAACmG,OAAO,EAAC,OAAO;YAAArB,QAAA,EAAC;UAG5B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACR,CAAC,EAEPtC,cAAc,CAACc,SAAS,gBACvB9B,OAAA,CAAC7B,GAAG;UAACiG,EAAE,EAAE;YAAE8C,SAAS,EAAE,QAAQ;YAAE7B,EAAE,EAAE;UAAE,CAAE;UAAAnC,QAAA,EACrC,OAAOlC,cAAc,CAACc,SAAS,KAAK,QAAQ,gBAC3C9B,OAAA;YACEmH,GAAG,EAAE,yBAAyBnG,cAAc,CAACc,SAAS,EAAG;YACzDsF,GAAG,EAAC,gBAAgB;YACpBC,KAAK,EAAE;cAAEC,QAAQ,EAAE,MAAM;cAAEZ,MAAM,EAAE;YAAO,CAAE;YAC5Ca,OAAO,EAAGC,CAAC,IAAK;cACdnF,OAAO,CAACf,KAAK,CAAC,gCAAgC,CAAC;cAC/CkG,CAAC,CAACC,MAAM,CAACJ,KAAK,CAACK,OAAO,GAAG,MAAM;YACjC;UAAE;YAAAvE,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACH,CAAC,GACAtC,cAAc,CAACc,SAAS,CAACK,IAAI,KAAK,QAAQ,gBAC5CnC,OAAA,CAAC9B,IAAI;YACH0H,IAAI,EAAElC,IAAI,CAACC,KAAK,CAAC3C,cAAc,CAACc,SAAS,CAACI,KAAK,CAAC,CAAC0D,IAAK;YACtD+B,MAAM,EAAE;cACN,GAAGjE,IAAI,CAACC,KAAK,CAAC3C,cAAc,CAACc,SAAS,CAACI,KAAK,CAAC,CAACyF,MAAM;cACpDC,QAAQ,EAAE,IAAI;cACdlB,MAAM,EAAE;YACV,CAAE;YACFmB,MAAM,EAAE;cAAEC,UAAU,EAAE;YAAK,CAAE;YAC7BT,KAAK,EAAE;cAAEb,KAAK,EAAE,MAAM;cAAEE,MAAM,EAAE;YAAQ;UAAE;YAAAvD,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC3C,CAAC,gBAEFtD,OAAA,CAAC5B,UAAU;YAAC6G,KAAK,EAAC,OAAO;YAAA/B,QAAA,EAAC;UAA4B;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAY;QACnE;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CAAC,gBAENtD,OAAA,CAAC7B,GAAG;UAACiG,EAAE,EAAE;YAAE8C,SAAS,EAAE,QAAQ;YAAE7C,CAAC,EAAE;UAAE,CAAE;UAAAnB,QAAA,gBACrClD,OAAA,CAAC5B,UAAU;YAAC6G,KAAK,EAAC,gBAAgB;YAAA/B,QAAA,EAC/BxC,SAAS,KAAK,CAAC,GAAG,0BAA0B,GAAG;UAA6B;YAAAyC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACnE,CAAC,EACZ5C,SAAS,KAAK,CAAC,iBACdV,OAAA,CAAC3B,MAAM;YACLkG,OAAO,EAAC,UAAU;YAClBwD,OAAO,EAAEA,CAAA,KAAMlG,iBAAiB,CAAC,WAAW,CAAE;YAC9CuC,EAAE,EAAE;cAAEiB,EAAE,EAAE;YAAE,CAAE;YAAAnC,QAAA,EACf;UAED;YAAAC,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAQ,CACT;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACE,CACN,eAEDtD,OAAA,CAACtB,OAAO;UAAC0F,EAAE,EAAE;YAAEkB,EAAE,EAAE;UAAE;QAAE;UAAAnC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAE1BtD,OAAA,CAAC5B,UAAU;UAACmG,OAAO,EAAC,WAAW;UAACC,YAAY;UAAAtB,QAAA,EAAC;QAE7C;UAAAC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAY,CAAC,eAEbtD,OAAA,CAACnB,IAAI;UAAAqE,QAAA,gBACHlD,OAAA,CAAClB,QAAQ;YAAAoE,QAAA,gBACPlD,OAAA,CAACjB,YAAY;cAAAmE,QAAA,eACXlD,OAAA,CAACP,IAAI;gBAACwF,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACZ,CAAC,eACftD,OAAA,CAAChB,YAAY;cACXuG,OAAO,EAAC,qBAAqB;cAC7BC,SAAS,EAAC;YAA2E;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACtF,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC,eAEXtD,OAAA,CAAClB,QAAQ;YAAAoE,QAAA,gBACPlD,OAAA,CAACjB,YAAY;cAAAmE,QAAA,eACXlD,OAAA,CAACN,OAAO;gBAACuF,KAAK,EAAC;cAAS;gBAAA9B,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAE;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACf,CAAC,eACftD,OAAA,CAAChB,YAAY;cACXuG,OAAO,EAAC,uBAAuB;cAC/BC,SAAS,EAAC;YAAgG;cAAArC,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3G,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACM,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACP,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACF;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACL,CAAC;EAEV,CAAC;EAED,oBACEtD,OAAA,CAAC7B,GAAG;IAAA+E,QAAA,gBACFlD,OAAA,CAAC5B,UAAU;MAACmG,OAAO,EAAC,IAAI;MAACC,YAAY;MAAAtB,QAAA,EAAC;IAEtC;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eACbtD,OAAA,CAAC5B,UAAU;MAACmG,OAAO,EAAC,OAAO;MAACU,KAAK,EAAC,gBAAgB;MAACgD,SAAS;MAAA/E,QAAA,EAAC;IAG7D;MAAAC,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAAY,CAAC,eAEbtD,OAAA,CAAC1B,KAAK;MAAC8F,EAAE,EAAE;QAAEE,EAAE,EAAE;MAAE,CAAE;MAAApB,QAAA,eACnBlD,OAAA,CAACd,IAAI;QACHgJ,KAAK,EAAExH,SAAU;QACjByH,QAAQ,EAAE1G,eAAgB;QAC1B8C,OAAO,EAAC,WAAW;QACnB6D,cAAc,EAAC,SAAS;QACxBC,SAAS,EAAC,SAAS;QAAAnF,QAAA,gBAEnBlD,OAAA,CAACf,GAAG;UAACqJ,IAAI,eAAEtI,OAAA,CAACX,QAAQ;YAAA8D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAa;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CtD,OAAA,CAACf,GAAG;UAACqJ,IAAI,eAAEtI,OAAA,CAACV,QAAQ;YAAA6D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAa;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CtD,OAAA,CAACf,GAAG;UAACqJ,IAAI,eAAEtI,OAAA,CAACT,WAAW;YAAA4D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAU;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC,eAC/CtD,OAAA,CAACf,GAAG;UAACqJ,IAAI,eAAEtI,OAAA,CAACR,UAAU;YAAA2D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAE;UAAC6B,KAAK,EAAC;QAAW;UAAAhC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAC3C;IAAC;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACF,CAAC,EAEP5C,SAAS,KAAK,CAAC,IAAI4B,wBAAwB,CAAC,CAAC,EAC7C5B,SAAS,KAAK,CAAC,IAAIuG,uBAAuB,CAAC,CAAC,EAC5CvG,SAAS,KAAK,CAAC,IAAI+E,uBAAuB,CAAC,CAAC,EAC5C/E,SAAS,KAAK,CAAC,IAAIsH,mBAAmB,CAAC,CAAC,eAEzChI,OAAA,CAAC7B,GAAG;MAACiG,EAAE,EAAE;QAAEsD,OAAO,EAAE,MAAM;QAAEa,cAAc,EAAE,eAAe;QAAElD,EAAE,EAAE;MAAE,CAAE;MAAAnC,QAAA,gBACnElD,OAAA,CAAC3B,MAAM;QACLkG,OAAO,EAAC,UAAU;QAClBwD,OAAO,EAAE1H,MAAO;QAChBmI,SAAS,eAAExI,OAAA,CAACb,SAAS;UAAAgE,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAC1B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC,eAETtD,OAAA,CAAC3B,MAAM;QACLkG,OAAO,EAAC,WAAW;QACnBU,KAAK,EAAC,SAAS;QACf8C,OAAO,EAAE3H,MAAO;QAChBqI,OAAO,eAAEzI,OAAA,CAACZ,YAAY;UAAA+D,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAE,CAAE;QAAAJ,QAAA,EAC3B;MAED;QAAAC,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAQ,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACN,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV,CAAC;AAAC7C,EAAA,CAtnBIN,eAAe;AAAAuI,EAAA,GAAfvI,eAAe;AAwnBrB,eAAeA,eAAe;AAAC,IAAAuI,EAAA;AAAAC,YAAA,CAAAD,EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}