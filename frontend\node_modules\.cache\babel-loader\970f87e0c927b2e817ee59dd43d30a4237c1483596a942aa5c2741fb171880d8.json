{"ast": null, "code": "\"use strict\";\n\nfunction _typeof(obj) {\n  \"@babel/helpers - typeof\";\n\n  return _typeof = \"function\" == typeof Symbol && \"symbol\" == typeof Symbol.iterator ? function (obj) {\n    return typeof obj;\n  } : function (obj) {\n    return obj && \"function\" == typeof Symbol && obj.constructor === Symbol && obj !== Symbol.prototype ? \"symbol\" : typeof obj;\n  }, _typeof(obj);\n}\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = plotComponentFactory;\nvar _react = _interopRequireWildcard(require(\"react\"));\nvar _propTypes = _interopRequireDefault(require(\"prop-types\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nfunction _getRequireWildcardCache(nodeInterop) {\n  if (typeof WeakMap !== \"function\") return null;\n  var cacheBabelInterop = new WeakMap();\n  var cacheNodeInterop = new WeakMap();\n  return (_getRequireWildcardCache = function _getRequireWildcardCache(nodeInterop) {\n    return nodeInterop ? cacheNodeInterop : cacheBabelInterop;\n  })(nodeInterop);\n}\nfunction _interopRequireWildcard(obj, nodeInterop) {\n  if (!nodeInterop && obj && obj.__esModule) {\n    return obj;\n  }\n  if (obj === null || _typeof(obj) !== \"object\" && typeof obj !== \"function\") {\n    return {\n      \"default\": obj\n    };\n  }\n  var cache = _getRequireWildcardCache(nodeInterop);\n  if (cache && cache.has(obj)) {\n    return cache.get(obj);\n  }\n  var newObj = {};\n  var hasPropertyDescriptor = Object.defineProperty && Object.getOwnPropertyDescriptor;\n  for (var key in obj) {\n    if (key !== \"default\" && Object.prototype.hasOwnProperty.call(obj, key)) {\n      var desc = hasPropertyDescriptor ? Object.getOwnPropertyDescriptor(obj, key) : null;\n      if (desc && (desc.get || desc.set)) {\n        Object.defineProperty(newObj, key, desc);\n      } else {\n        newObj[key] = obj[key];\n      }\n    }\n  }\n  newObj[\"default\"] = obj;\n  if (cache) {\n    cache.set(obj, newObj);\n  }\n  return newObj;\n}\nfunction _classCallCheck(instance, Constructor) {\n  if (!(instance instanceof Constructor)) {\n    throw new TypeError(\"Cannot call a class as a function\");\n  }\n}\nfunction _defineProperties(target, props) {\n  for (var i = 0; i < props.length; i++) {\n    var descriptor = props[i];\n    descriptor.enumerable = descriptor.enumerable || false;\n    descriptor.configurable = true;\n    if (\"value\" in descriptor) descriptor.writable = true;\n    Object.defineProperty(target, descriptor.key, descriptor);\n  }\n}\nfunction _createClass(Constructor, protoProps, staticProps) {\n  if (protoProps) _defineProperties(Constructor.prototype, protoProps);\n  if (staticProps) _defineProperties(Constructor, staticProps);\n  Object.defineProperty(Constructor, \"prototype\", {\n    writable: false\n  });\n  return Constructor;\n}\nfunction _inherits(subClass, superClass) {\n  if (typeof superClass !== \"function\" && superClass !== null) {\n    throw new TypeError(\"Super expression must either be null or a function\");\n  }\n  subClass.prototype = Object.create(superClass && superClass.prototype, {\n    constructor: {\n      value: subClass,\n      writable: true,\n      configurable: true\n    }\n  });\n  Object.defineProperty(subClass, \"prototype\", {\n    writable: false\n  });\n  if (superClass) _setPrototypeOf(subClass, superClass);\n}\nfunction _setPrototypeOf(o, p) {\n  _setPrototypeOf = Object.setPrototypeOf ? Object.setPrototypeOf.bind() : function _setPrototypeOf(o, p) {\n    o.__proto__ = p;\n    return o;\n  };\n  return _setPrototypeOf(o, p);\n}\nfunction _createSuper(Derived) {\n  var hasNativeReflectConstruct = _isNativeReflectConstruct();\n  return function _createSuperInternal() {\n    var Super = _getPrototypeOf(Derived),\n      result;\n    if (hasNativeReflectConstruct) {\n      var NewTarget = _getPrototypeOf(this).constructor;\n      result = Reflect.construct(Super, arguments, NewTarget);\n    } else {\n      result = Super.apply(this, arguments);\n    }\n    return _possibleConstructorReturn(this, result);\n  };\n}\nfunction _possibleConstructorReturn(self, call) {\n  if (call && (_typeof(call) === \"object\" || typeof call === \"function\")) {\n    return call;\n  } else if (call !== void 0) {\n    throw new TypeError(\"Derived constructors may only return object or undefined\");\n  }\n  return _assertThisInitialized(self);\n}\nfunction _assertThisInitialized(self) {\n  if (self === void 0) {\n    throw new ReferenceError(\"this hasn't been initialised - super() hasn't been called\");\n  }\n  return self;\n}\nfunction _isNativeReflectConstruct() {\n  if (typeof Reflect === \"undefined\" || !Reflect.construct) return false;\n  if (Reflect.construct.sham) return false;\n  if (typeof Proxy === \"function\") return true;\n  try {\n    Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {}));\n    return true;\n  } catch (e) {\n    return false;\n  }\n}\nfunction _getPrototypeOf(o) {\n  _getPrototypeOf = Object.setPrototypeOf ? Object.getPrototypeOf.bind() : function _getPrototypeOf(o) {\n    return o.__proto__ || Object.getPrototypeOf(o);\n  };\n  return _getPrototypeOf(o);\n}\n\n// The naming convention is:\n//   - events are attached as `'plotly_' + eventName.toLowerCase()`\n//   - react props are `'on' + eventName`\nvar eventNames = ['AfterExport', 'AfterPlot', 'Animated', 'AnimatingFrame', 'AnimationInterrupted', 'AutoSize', 'BeforeExport', 'BeforeHover', 'ButtonClicked', 'Click', 'ClickAnnotation', 'Deselect', 'DoubleClick', 'Framework', 'Hover', 'LegendClick', 'LegendDoubleClick', 'Relayout', 'Relayouting', 'Restyle', 'Redraw', 'Selected', 'Selecting', 'SliderChange', 'SliderEnd', 'SliderStart', 'SunburstClick', 'Transitioning', 'TransitionInterrupted', 'Unhover', 'WebGlContextLost'];\nvar updateEvents = ['plotly_restyle', 'plotly_redraw', 'plotly_relayout', 'plotly_relayouting', 'plotly_doubleclick', 'plotly_animated', 'plotly_sunburstclick']; // Check if a window is available since SSR (server-side rendering)\n// breaks unnecessarily if you try to use it server-side.\n\nvar isBrowser = typeof window !== 'undefined';\nfunction plotComponentFactory(Plotly) {\n  var PlotlyComponent = /*#__PURE__*/function (_Component) {\n    _inherits(PlotlyComponent, _Component);\n    var _super = _createSuper(PlotlyComponent);\n    function PlotlyComponent(props) {\n      var _this;\n      _classCallCheck(this, PlotlyComponent);\n      _this = _super.call(this, props);\n      _this.p = Promise.resolve();\n      _this.resizeHandler = null;\n      _this.handlers = {};\n      _this.syncWindowResize = _this.syncWindowResize.bind(_assertThisInitialized(_this));\n      _this.syncEventHandlers = _this.syncEventHandlers.bind(_assertThisInitialized(_this));\n      _this.attachUpdateEvents = _this.attachUpdateEvents.bind(_assertThisInitialized(_this));\n      _this.getRef = _this.getRef.bind(_assertThisInitialized(_this));\n      _this.handleUpdate = _this.handleUpdate.bind(_assertThisInitialized(_this));\n      _this.figureCallback = _this.figureCallback.bind(_assertThisInitialized(_this));\n      _this.updatePlotly = _this.updatePlotly.bind(_assertThisInitialized(_this));\n      return _this;\n    }\n    _createClass(PlotlyComponent, [{\n      key: \"updatePlotly\",\n      value: function updatePlotly(shouldInvokeResizeHandler, figureCallbackFunction, shouldAttachUpdateEvents) {\n        var _this2 = this;\n        this.p = this.p.then(function () {\n          if (_this2.unmounting) {\n            return;\n          }\n          if (!_this2.el) {\n            throw new Error('Missing element reference');\n          } // eslint-disable-next-line consistent-return\n\n          return Plotly.react(_this2.el, {\n            data: _this2.props.data,\n            layout: _this2.props.layout,\n            config: _this2.props.config,\n            frames: _this2.props.frames\n          });\n        }).then(function () {\n          if (_this2.unmounting) {\n            return;\n          }\n          _this2.syncWindowResize(shouldInvokeResizeHandler);\n          _this2.syncEventHandlers();\n          _this2.figureCallback(figureCallbackFunction);\n          if (shouldAttachUpdateEvents) {\n            _this2.attachUpdateEvents();\n          }\n        })[\"catch\"](function (err) {\n          if (_this2.props.onError) {\n            _this2.props.onError(err);\n          }\n        });\n      }\n    }, {\n      key: \"componentDidMount\",\n      value: function componentDidMount() {\n        this.unmounting = false;\n        this.updatePlotly(true, this.props.onInitialized, true);\n      }\n    }, {\n      key: \"componentDidUpdate\",\n      value: function componentDidUpdate(prevProps) {\n        this.unmounting = false; // frames *always* changes identity so fall back to check length only :(\n\n        var numPrevFrames = prevProps.frames && prevProps.frames.length ? prevProps.frames.length : 0;\n        var numNextFrames = this.props.frames && this.props.frames.length ? this.props.frames.length : 0;\n        var figureChanged = !(prevProps.layout === this.props.layout && prevProps.data === this.props.data && prevProps.config === this.props.config && numNextFrames === numPrevFrames);\n        var revisionDefined = prevProps.revision !== void 0;\n        var revisionChanged = prevProps.revision !== this.props.revision;\n        if (!figureChanged && (!revisionDefined || revisionDefined && !revisionChanged)) {\n          return;\n        }\n        this.updatePlotly(false, this.props.onUpdate, false);\n      }\n    }, {\n      key: \"componentWillUnmount\",\n      value: function componentWillUnmount() {\n        this.unmounting = true;\n        this.figureCallback(this.props.onPurge);\n        if (this.resizeHandler && isBrowser) {\n          window.removeEventListener('resize', this.resizeHandler);\n          this.resizeHandler = null;\n        }\n        this.removeUpdateEvents();\n        Plotly.purge(this.el);\n      }\n    }, {\n      key: \"attachUpdateEvents\",\n      value: function attachUpdateEvents() {\n        var _this3 = this;\n        if (!this.el || !this.el.removeListener) {\n          return;\n        }\n        updateEvents.forEach(function (updateEvent) {\n          _this3.el.on(updateEvent, _this3.handleUpdate);\n        });\n      }\n    }, {\n      key: \"removeUpdateEvents\",\n      value: function removeUpdateEvents() {\n        var _this4 = this;\n        if (!this.el || !this.el.removeListener) {\n          return;\n        }\n        updateEvents.forEach(function (updateEvent) {\n          _this4.el.removeListener(updateEvent, _this4.handleUpdate);\n        });\n      }\n    }, {\n      key: \"handleUpdate\",\n      value: function handleUpdate() {\n        this.figureCallback(this.props.onUpdate);\n      }\n    }, {\n      key: \"figureCallback\",\n      value: function figureCallback(callback) {\n        if (typeof callback === 'function') {\n          var _this$el = this.el,\n            data = _this$el.data,\n            layout = _this$el.layout;\n          var frames = this.el._transitionData ? this.el._transitionData._frames : null;\n          var figure = {\n            data: data,\n            layout: layout,\n            frames: frames\n          };\n          callback(figure, this.el);\n        }\n      }\n    }, {\n      key: \"syncWindowResize\",\n      value: function syncWindowResize(invoke) {\n        var _this5 = this;\n        if (!isBrowser) {\n          return;\n        }\n        if (this.props.useResizeHandler && !this.resizeHandler) {\n          this.resizeHandler = function () {\n            return Plotly.Plots.resize(_this5.el);\n          };\n          window.addEventListener('resize', this.resizeHandler);\n          if (invoke) {\n            this.resizeHandler();\n          }\n        } else if (!this.props.useResizeHandler && this.resizeHandler) {\n          window.removeEventListener('resize', this.resizeHandler);\n          this.resizeHandler = null;\n        }\n      }\n    }, {\n      key: \"getRef\",\n      value: function getRef(el) {\n        this.el = el;\n        if (this.props.debug && isBrowser) {\n          window.gd = this.el;\n        }\n      } // Attach and remove event handlers as they're added or removed from props:\n    }, {\n      key: \"syncEventHandlers\",\n      value: function syncEventHandlers() {\n        var _this6 = this;\n        eventNames.forEach(function (eventName) {\n          var prop = _this6.props['on' + eventName];\n          var handler = _this6.handlers[eventName];\n          var hasHandler = Boolean(handler);\n          if (prop && !hasHandler) {\n            _this6.addEventHandler(eventName, prop);\n          } else if (!prop && hasHandler) {\n            // Needs to be removed:\n            _this6.removeEventHandler(eventName);\n          } else if (prop && hasHandler && prop !== handler) {\n            // replace the handler\n            _this6.removeEventHandler(eventName);\n            _this6.addEventHandler(eventName, prop);\n          }\n        });\n      }\n    }, {\n      key: \"addEventHandler\",\n      value: function addEventHandler(eventName, prop) {\n        this.handlers[eventName] = prop;\n        this.el.on(this.getPlotlyEventName(eventName), this.handlers[eventName]);\n      }\n    }, {\n      key: \"removeEventHandler\",\n      value: function removeEventHandler(eventName) {\n        this.el.removeListener(this.getPlotlyEventName(eventName), this.handlers[eventName]);\n        delete this.handlers[eventName];\n      }\n    }, {\n      key: \"getPlotlyEventName\",\n      value: function getPlotlyEventName(eventName) {\n        return 'plotly_' + eventName.toLowerCase();\n      }\n    }, {\n      key: \"render\",\n      value: function render() {\n        return /*#__PURE__*/_react[\"default\"].createElement(\"div\", {\n          id: this.props.divId,\n          style: this.props.style,\n          ref: this.getRef,\n          className: this.props.className\n        });\n      }\n    }]);\n    return PlotlyComponent;\n  }(_react.Component);\n  PlotlyComponent.propTypes = {\n    data: _propTypes[\"default\"].arrayOf(_propTypes[\"default\"].object),\n    config: _propTypes[\"default\"].object,\n    layout: _propTypes[\"default\"].object,\n    frames: _propTypes[\"default\"].arrayOf(_propTypes[\"default\"].object),\n    revision: _propTypes[\"default\"].number,\n    onInitialized: _propTypes[\"default\"].func,\n    onPurge: _propTypes[\"default\"].func,\n    onError: _propTypes[\"default\"].func,\n    onUpdate: _propTypes[\"default\"].func,\n    debug: _propTypes[\"default\"].bool,\n    style: _propTypes[\"default\"].object,\n    className: _propTypes[\"default\"].string,\n    useResizeHandler: _propTypes[\"default\"].bool,\n    divId: _propTypes[\"default\"].string\n  };\n  eventNames.forEach(function (eventName) {\n    PlotlyComponent.propTypes['on' + eventName] = _propTypes[\"default\"].func;\n  });\n  PlotlyComponent.defaultProps = {\n    debug: false,\n    useResizeHandler: false,\n    data: [],\n    style: {\n      position: 'relative',\n      display: 'inline-block'\n    }\n  };\n  return PlotlyComponent;\n}", "map": {"version": 3, "names": ["_react", "_interopRequireWildcard", "require", "_propTypes", "_interopRequireDefault", "eventNames", "updateEvents", "<PERSON><PERSON><PERSON><PERSON>", "window", "plotComponentFactory", "<PERSON><PERSON><PERSON>", "PlotlyComponent", "_Component", "_inherits", "_super", "_createSuper", "props", "_this", "_classCallCheck", "call", "p", "Promise", "resolve", "resize<PERSON><PERSON>ler", "handlers", "syncWindowResize", "bind", "_assertThisInitialized", "syncEventHandlers", "attachUpdateEvents", "getRef", "handleUpdate", "figure<PERSON><PERSON>back", "updatePlotly", "_createClass", "key", "value", "shouldInvokeResizeHandler", "figureCallbackFunction", "shouldAttachUpdateEvents", "_this2", "then", "unmounting", "el", "Error", "react", "data", "layout", "config", "frames", "err", "onError", "componentDidMount", "onInitialized", "componentDidUpdate", "prevProps", "numPrevFrames", "length", "numNextFrames", "figureChanged", "revisionDefined", "revision", "revisionChanged", "onUpdate", "componentWillUnmount", "onPurge", "removeEventListener", "removeUpdateEvents", "purge", "_this3", "removeListener", "for<PERSON>ach", "updateEvent", "on", "_this4", "callback", "_this$el", "_transitionData", "_frames", "figure", "invoke", "_this5", "useResizeHandler", "Plots", "resize", "addEventListener", "debug", "gd", "_this6", "eventName", "prop", "handler", "<PERSON><PERSON><PERSON><PERSON>", "Boolean", "addEventHandler", "removeEventHandler", "getPlotlyEventName", "toLowerCase", "render", "createElement", "id", "divId", "style", "ref", "className", "Component", "propTypes", "arrayOf", "object", "number", "func", "bool", "string", "defaultProps", "position", "display"], "sources": ["C:\\Users\\<USER>\\Pumptimemodel\\frontend\\node_modules\\src\\factory.js"], "sourcesContent": ["import React, {Component} from 'react';\nimport PropTypes from 'prop-types';\n\n// The naming convention is:\n//   - events are attached as `'plotly_' + eventName.toLowerCase()`\n//   - react props are `'on' + eventName`\nconst eventNames = [\n  'AfterExport',\n  'AfterPlot',\n  'Animated',\n  'AnimatingFrame',\n  'AnimationInterrupted',\n  'AutoSize',\n  'BeforeExport',\n  'BeforeHover',\n  'ButtonClicked',\n  'Click',\n  'ClickAnnotation',\n  'Deselect',\n  'DoubleClick',\n  'Framework',\n  'Hover',\n  'LegendClick',\n  'LegendDoubleClick',\n  'Relayout',\n  'Relayouting',\n  'Restyle',\n  'Redraw',\n  'Selected',\n  'Selecting',\n  'SliderChange',\n  'SliderEnd',\n  'SliderStart',\n  'SunburstClick',\n  'Transitioning',\n  'TransitionInterrupted',\n  'Unhover',\n  'WebGlContextLost',\n];\n\nconst updateEvents = [\n  'plotly_restyle',\n  'plotly_redraw',\n  'plotly_relayout',\n  'plotly_relayouting',\n  'plotly_doubleclick',\n  'plotly_animated',\n  'plotly_sunburstclick',\n];\n\n// Check if a window is available since SSR (server-side rendering)\n// breaks unnecessarily if you try to use it server-side.\nconst isBrowser = typeof window !== 'undefined';\n\nexport default function plotComponentFactory(Plotly) {\n  class PlotlyComponent extends Component {\n    constructor(props) {\n      super(props);\n\n      this.p = Promise.resolve();\n      this.resizeHandler = null;\n      this.handlers = {};\n\n      this.syncWindowResize = this.syncWindowResize.bind(this);\n      this.syncEventHandlers = this.syncEventHandlers.bind(this);\n      this.attachUpdateEvents = this.attachUpdateEvents.bind(this);\n      this.getRef = this.getRef.bind(this);\n      this.handleUpdate = this.handleUpdate.bind(this);\n      this.figureCallback = this.figureCallback.bind(this);\n      this.updatePlotly = this.updatePlotly.bind(this);\n    }\n\n    updatePlotly(shouldInvokeResizeHandler, figureCallbackFunction, shouldAttachUpdateEvents) {\n      this.p = this.p\n        .then(() => {\n          if (this.unmounting) {\n            return;\n          }\n          if (!this.el) {\n            throw new Error('Missing element reference');\n          }\n          // eslint-disable-next-line consistent-return\n          return Plotly.react(this.el, {\n            data: this.props.data,\n            layout: this.props.layout,\n            config: this.props.config,\n            frames: this.props.frames,\n          });\n        })\n        .then(() => {\n          if (this.unmounting) {\n            return;\n          }\n          this.syncWindowResize(shouldInvokeResizeHandler);\n          this.syncEventHandlers();\n          this.figureCallback(figureCallbackFunction);\n          if (shouldAttachUpdateEvents) {\n            this.attachUpdateEvents();\n          }\n        })\n        .catch((err) => {\n          if (this.props.onError) {\n            this.props.onError(err);\n          }\n        });\n    }\n\n    componentDidMount() {\n      this.unmounting = false;\n\n      this.updatePlotly(true, this.props.onInitialized, true);\n    }\n\n    componentDidUpdate(prevProps) {\n      this.unmounting = false;\n\n      // frames *always* changes identity so fall back to check length only :(\n      const numPrevFrames =\n        prevProps.frames && prevProps.frames.length ? prevProps.frames.length : 0;\n      const numNextFrames =\n        this.props.frames && this.props.frames.length ? this.props.frames.length : 0;\n\n      const figureChanged = !(\n        prevProps.layout === this.props.layout &&\n        prevProps.data === this.props.data &&\n        prevProps.config === this.props.config &&\n        numNextFrames === numPrevFrames\n      );\n      const revisionDefined = prevProps.revision !== void 0;\n      const revisionChanged = prevProps.revision !== this.props.revision;\n\n      if (!figureChanged && (!revisionDefined || (revisionDefined && !revisionChanged))) {\n        return;\n      }\n\n      this.updatePlotly(false, this.props.onUpdate, false);\n    }\n\n    componentWillUnmount() {\n      this.unmounting = true;\n\n      this.figureCallback(this.props.onPurge);\n\n      if (this.resizeHandler && isBrowser) {\n        window.removeEventListener('resize', this.resizeHandler);\n        this.resizeHandler = null;\n      }\n\n      this.removeUpdateEvents();\n\n      Plotly.purge(this.el);\n    }\n\n    attachUpdateEvents() {\n      if (!this.el || !this.el.removeListener) {\n        return;\n      }\n\n      updateEvents.forEach((updateEvent) => {\n        this.el.on(updateEvent, this.handleUpdate);\n      });\n    }\n\n    removeUpdateEvents() {\n      if (!this.el || !this.el.removeListener) {\n        return;\n      }\n\n      updateEvents.forEach((updateEvent) => {\n        this.el.removeListener(updateEvent, this.handleUpdate);\n      });\n    }\n\n    handleUpdate() {\n      this.figureCallback(this.props.onUpdate);\n    }\n\n    figureCallback(callback) {\n      if (typeof callback === 'function') {\n        const {data, layout} = this.el;\n        const frames = this.el._transitionData ? this.el._transitionData._frames : null;\n        const figure = {data, layout, frames};\n        callback(figure, this.el);\n      }\n    }\n\n    syncWindowResize(invoke) {\n      if (!isBrowser) {\n        return;\n      }\n\n      if (this.props.useResizeHandler && !this.resizeHandler) {\n        this.resizeHandler = () => Plotly.Plots.resize(this.el);\n        window.addEventListener('resize', this.resizeHandler);\n        if (invoke) {\n          this.resizeHandler();\n        }\n      } else if (!this.props.useResizeHandler && this.resizeHandler) {\n        window.removeEventListener('resize', this.resizeHandler);\n        this.resizeHandler = null;\n      }\n    }\n\n    getRef(el) {\n      this.el = el;\n\n      if (this.props.debug && isBrowser) {\n        window.gd = this.el;\n      }\n    }\n\n    // Attach and remove event handlers as they're added or removed from props:\n    syncEventHandlers() {\n      eventNames.forEach((eventName) => {\n        const prop = this.props['on' + eventName];\n        const handler = this.handlers[eventName];\n        const hasHandler = Boolean(handler);\n\n        if (prop && !hasHandler) {\n          this.addEventHandler(eventName, prop);\n        } else if (!prop && hasHandler) {\n          // Needs to be removed:\n          this.removeEventHandler(eventName);\n        } else if (prop && hasHandler && prop !== handler) {\n          // replace the handler\n          this.removeEventHandler(eventName);\n          this.addEventHandler(eventName, prop);\n        }\n      });\n    }\n\n    addEventHandler(eventName, prop) {\n      this.handlers[eventName] = prop;\n      this.el.on(this.getPlotlyEventName(eventName), this.handlers[eventName]);\n    }\n\n    removeEventHandler(eventName) {\n      this.el.removeListener(this.getPlotlyEventName(eventName), this.handlers[eventName]);\n      delete this.handlers[eventName];\n    }\n\n    getPlotlyEventName(eventName) {\n      return 'plotly_' + eventName.toLowerCase();\n    }\n\n    render() {\n      return (\n        <div\n          id={this.props.divId}\n          style={this.props.style}\n          ref={this.getRef}\n          className={this.props.className}\n        />\n      );\n    }\n  }\n\n  PlotlyComponent.propTypes = {\n    data: PropTypes.arrayOf(PropTypes.object),\n    config: PropTypes.object,\n    layout: PropTypes.object,\n    frames: PropTypes.arrayOf(PropTypes.object),\n    revision: PropTypes.number,\n    onInitialized: PropTypes.func,\n    onPurge: PropTypes.func,\n    onError: PropTypes.func,\n    onUpdate: PropTypes.func,\n    debug: PropTypes.bool,\n    style: PropTypes.object,\n    className: PropTypes.string,\n    useResizeHandler: PropTypes.bool,\n    divId: PropTypes.string,\n  };\n\n  eventNames.forEach((eventName) => {\n    PlotlyComponent.propTypes['on' + eventName] = PropTypes.func;\n  });\n\n  PlotlyComponent.defaultProps = {\n    debug: false,\n    useResizeHandler: false,\n    data: [],\n    style: {position: 'relative', display: 'inline-block'},\n  };\n\n  return PlotlyComponent;\n}\n"], "mappings": ";;;;;;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,UAAA,GAAAC,sBAAA,CAAAF,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA;AACA;AACA;AACA,IAAMG,UAAU,GAAG,CACjB,aADiB,EAEjB,WAFiB,EAGjB,UAHiB,EAIjB,gBAJiB,EAKjB,sBALiB,EAMjB,UANiB,EAOjB,cAPiB,EAQjB,aARiB,EASjB,eATiB,EAUjB,OAViB,EAWjB,iBAXiB,EAYjB,UAZiB,EAajB,aAbiB,EAcjB,WAdiB,EAejB,OAfiB,EAgBjB,aAhBiB,EAiBjB,mBAjBiB,EAkBjB,UAlBiB,EAmBjB,aAnBiB,EAoBjB,SApBiB,EAqBjB,QArBiB,EAsBjB,UAtBiB,EAuBjB,WAvBiB,EAwBjB,cAxBiB,EAyBjB,WAzBiB,EA0BjB,aA1BiB,EA2BjB,eA3BiB,EA4BjB,eA5BiB,EA6BjB,uBA7BiB,EA8BjB,SA9BiB,EA+BjB,kBA/BiB,CAAnB;AAkCA,IAAMC,YAAY,GAAG,CACnB,gBADmB,EAEnB,eAFmB,EAGnB,iBAHmB,EAInB,oBAJmB,EAKnB,oBALmB,EAMnB,iBANmB,EAOnB,sBAPmB,CAArB,C,CAUA;AACA;;AACA,IAAMC,SAAS,GAAG,OAAOC,MAAP,KAAkB,WAApC;AAEe,SAASC,oBAATA,CAA8BC,MAA9B,EAAsC;EAAA,IAC7CC,eAD6C,0BAAAC,UAAA;IAAAC,SAAA,CAAAF,eAAA,EAAAC,UAAA;IAAA,IAAAE,MAAA,GAAAC,YAAA,CAAAJ,eAAA;IAEjD,SAAAA,gBAAYK,KAAZ,EAAmB;MAAA,IAAAC,KAAA;MAAAC,eAAA,OAAAP,eAAA;MACjBM,KAAA,GAAAH,MAAA,CAAAK,IAAA,OAAMH,KAAN;MAEAC,KAAA,CAAKG,CAAL,GAASC,OAAO,CAACC,OAAR,EAAT;MACAL,KAAA,CAAKM,aAAL,GAAqB,IAArB;MACAN,KAAA,CAAKO,QAAL,GAAgB,EAAhB;MAEAP,KAAA,CAAKQ,gBAAL,GAAwBR,KAAA,CAAKQ,gBAAL,CAAsBC,IAAtB,CAAAC,sBAAA,CAAAV,KAAA,EAAxB;MACAA,KAAA,CAAKW,iBAAL,GAAyBX,KAAA,CAAKW,iBAAL,CAAuBF,IAAvB,CAAAC,sBAAA,CAAAV,KAAA,EAAzB;MACAA,KAAA,CAAKY,kBAAL,GAA0BZ,KAAA,CAAKY,kBAAL,CAAwBH,IAAxB,CAAAC,sBAAA,CAAAV,KAAA,EAA1B;MACAA,KAAA,CAAKa,MAAL,GAAcb,KAAA,CAAKa,MAAL,CAAYJ,IAAZ,CAAAC,sBAAA,CAAAV,KAAA,EAAd;MACAA,KAAA,CAAKc,YAAL,GAAoBd,KAAA,CAAKc,YAAL,CAAkBL,IAAlB,CAAAC,sBAAA,CAAAV,KAAA,EAApB;MACAA,KAAA,CAAKe,cAAL,GAAsBf,KAAA,CAAKe,cAAL,CAAoBN,IAApB,CAAAC,sBAAA,CAAAV,KAAA,EAAtB;MACAA,KAAA,CAAKgB,YAAL,GAAoBhB,KAAA,CAAKgB,YAAL,CAAkBP,IAAlB,CAAAC,sBAAA,CAAAV,KAAA,EAApB;MAbiB,OAAAA,KAAA;IAclB;IAhBgDiB,YAAA,CAAAvB,eAAA;MAAAwB,GAAA;MAAAC,KAAA,EAkBjD,SAAAH,aAAaI,yBAAb,EAAwCC,sBAAxC,EAAgEC,wBAAhE,EAA0F;QAAA,IAAAC,MAAA;QACxF,KAAKpB,CAAL,GAAS,KAAKA,CAAL,CACNqB,IADM,CACD,YAAM;UACV,IAAID,MAAI,CAACE,UAAT,EAAqB;YACnB;UACD;UACD,IAAI,CAACF,MAAI,CAACG,EAAV,EAAc;YACZ,MAAM,IAAIC,KAAJ,CAAU,2BAAV,CAAN;UACD,CANS,CAOV;;UACA,OAAOlC,MAAM,CAACmC,KAAP,CAAaL,MAAI,CAACG,EAAlB,EAAsB;YAC3BG,IAAI,EAAEN,MAAI,CAACxB,KAAL,CAAW8B,IADU;YAE3BC,MAAM,EAAEP,MAAI,CAACxB,KAAL,CAAW+B,MAFQ;YAG3BC,MAAM,EAAER,MAAI,CAACxB,KAAL,CAAWgC,MAHQ;YAI3BC,MAAM,EAAET,MAAI,CAACxB,KAAL,CAAWiC;UAJQ,CAAtB,CAAP;QAMD,CAfM,EAgBNR,IAhBM,CAgBD,YAAM;UACV,IAAID,MAAI,CAACE,UAAT,EAAqB;YACnB;UACD;UACDF,MAAI,CAACf,gBAAL,CAAsBY,yBAAtB;UACAG,MAAI,CAACZ,iBAAL;UACAY,MAAI,CAACR,cAAL,CAAoBM,sBAApB;UACA,IAAIC,wBAAJ,EAA8B;YAC5BC,MAAI,CAACX,kBAAL;UACD;QACF,CA1BM,WA2BA,UAACqB,GAAD,EAAS;UACd,IAAIV,MAAI,CAACxB,KAAL,CAAWmC,OAAf,EAAwB;YACtBX,MAAI,CAACxB,KAAL,CAAWmC,OAAX,CAAmBD,GAAnB;UACD;QACF,CA/BM,CAAT;MAgCD;IAnDgD;MAAAf,GAAA;MAAAC,KAAA,EAqDjD,SAAAgB,kBAAA,EAAoB;QAClB,KAAKV,UAAL,GAAkB,KAAlB;QAEA,KAAKT,YAAL,CAAkB,IAAlB,EAAwB,KAAKjB,KAAL,CAAWqC,aAAnC,EAAkD,IAAlD;MACD;IAzDgD;MAAAlB,GAAA;MAAAC,KAAA,EA2DjD,SAAAkB,mBAAmBC,SAAnB,EAA8B;QAC5B,KAAKb,UAAL,GAAkB,KAAlB,CAD4B,CAG5B;;QACA,IAAMc,aAAa,GACjBD,SAAS,CAACN,MAAV,IAAoBM,SAAS,CAACN,MAAV,CAAiBQ,MAArC,GAA8CF,SAAS,CAACN,MAAV,CAAiBQ,MAA/D,GAAwE,CAD1E;QAEA,IAAMC,aAAa,GACjB,KAAK1C,KAAL,CAAWiC,MAAX,IAAqB,KAAKjC,KAAL,CAAWiC,MAAX,CAAkBQ,MAAvC,GAAgD,KAAKzC,KAAL,CAAWiC,MAAX,CAAkBQ,MAAlE,GAA2E,CAD7E;QAGA,IAAME,aAAa,GAAG,EACpBJ,SAAS,CAACR,MAAV,KAAqB,KAAK/B,KAAL,CAAW+B,MAAhC,IACAQ,SAAS,CAACT,IAAV,KAAmB,KAAK9B,KAAL,CAAW8B,IAD9B,IAEAS,SAAS,CAACP,MAAV,KAAqB,KAAKhC,KAAL,CAAWgC,MAFhC,IAGAU,aAAa,KAAKF,aAJE,CAAtB;QAMA,IAAMI,eAAe,GAAGL,SAAS,CAACM,QAAV,KAAuB,KAAK,CAApD;QACA,IAAMC,eAAe,GAAGP,SAAS,CAACM,QAAV,KAAuB,KAAK7C,KAAL,CAAW6C,QAA1D;QAEA,IAAI,CAACF,aAAD,KAAmB,CAACC,eAAD,IAAqBA,eAAe,IAAI,CAACE,eAA5D,CAAJ,EAAmF;UACjF;QACD;QAED,KAAK7B,YAAL,CAAkB,KAAlB,EAAyB,KAAKjB,KAAL,CAAW+C,QAApC,EAA8C,KAA9C;MACD;IAlFgD;MAAA5B,GAAA;MAAAC,KAAA,EAoFjD,SAAA4B,qBAAA,EAAuB;QACrB,KAAKtB,UAAL,GAAkB,IAAlB;QAEA,KAAKV,cAAL,CAAoB,KAAKhB,KAAL,CAAWiD,OAA/B;QAEA,IAAI,KAAK1C,aAAL,IAAsBhB,SAA1B,EAAqC;UACnCC,MAAM,CAAC0D,mBAAP,CAA2B,QAA3B,EAAqC,KAAK3C,aAA1C;UACA,KAAKA,aAAL,GAAqB,IAArB;QACD;QAED,KAAK4C,kBAAL;QAEAzD,MAAM,CAAC0D,KAAP,CAAa,KAAKzB,EAAlB;MACD;IAjGgD;MAAAR,GAAA;MAAAC,KAAA,EAmGjD,SAAAP,mBAAA,EAAqB;QAAA,IAAAwC,MAAA;QACnB,IAAI,CAAC,KAAK1B,EAAN,IAAY,CAAC,KAAKA,EAAL,CAAQ2B,cAAzB,EAAyC;UACvC;QACD;QAEDhE,YAAY,CAACiE,OAAb,CAAqB,UAACC,WAAD,EAAiB;UACpCH,MAAI,CAAC1B,EAAL,CAAQ8B,EAAR,CAAWD,WAAX,EAAwBH,MAAI,CAACtC,YAA7B;QACD,CAFD;MAGD;IA3GgD;MAAAI,GAAA;MAAAC,KAAA,EA6GjD,SAAA+B,mBAAA,EAAqB;QAAA,IAAAO,MAAA;QACnB,IAAI,CAAC,KAAK/B,EAAN,IAAY,CAAC,KAAKA,EAAL,CAAQ2B,cAAzB,EAAyC;UACvC;QACD;QAEDhE,YAAY,CAACiE,OAAb,CAAqB,UAACC,WAAD,EAAiB;UACpCE,MAAI,CAAC/B,EAAL,CAAQ2B,cAAR,CAAuBE,WAAvB,EAAoCE,MAAI,CAAC3C,YAAzC;QACD,CAFD;MAGD;IArHgD;MAAAI,GAAA;MAAAC,KAAA,EAuHjD,SAAAL,aAAA,EAAe;QACb,KAAKC,cAAL,CAAoB,KAAKhB,KAAL,CAAW+C,QAA/B;MACD;IAzHgD;MAAA5B,GAAA;MAAAC,KAAA,EA2HjD,SAAAJ,eAAe2C,QAAf,EAAyB;QACvB,IAAI,OAAOA,QAAP,KAAoB,UAAxB,EAAoC;UAClC,IAAAC,QAAA,GAAuB,KAAKjC,EAA5B;YAAOG,IAAP,GAAA8B,QAAA,CAAO9B,IAAP;YAAaC,MAAb,GAAA6B,QAAA,CAAa7B,MAAb;UACA,IAAME,MAAM,GAAG,KAAKN,EAAL,CAAQkC,eAAR,GAA0B,KAAKlC,EAAL,CAAQkC,eAAR,CAAwBC,OAAlD,GAA4D,IAA3E;UACA,IAAMC,MAAM,GAAG;YAACjC,IAAI,EAAJA,IAAD;YAAOC,MAAM,EAANA,MAAP;YAAeE,MAAM,EAANA;UAAf,CAAf;UACA0B,QAAQ,CAACI,MAAD,EAAS,KAAKpC,EAAd,CAAR;QACD;MACF;IAlIgD;MAAAR,GAAA;MAAAC,KAAA,EAoIjD,SAAAX,iBAAiBuD,MAAjB,EAAyB;QAAA,IAAAC,MAAA;QACvB,IAAI,CAAC1E,SAAL,EAAgB;UACd;QACD;QAED,IAAI,KAAKS,KAAL,CAAWkE,gBAAX,IAA+B,CAAC,KAAK3D,aAAzC,EAAwD;UACtD,KAAKA,aAAL,GAAqB;YAAA,OAAMb,MAAM,CAACyE,KAAP,CAAaC,MAAb,CAAoBH,MAAI,CAACtC,EAAzB,CAAN;UAAA,CAArB;UACAnC,MAAM,CAAC6E,gBAAP,CAAwB,QAAxB,EAAkC,KAAK9D,aAAvC;UACA,IAAIyD,MAAJ,EAAY;YACV,KAAKzD,aAAL;UACD;QACF,CAND,MAMO,IAAI,CAAC,KAAKP,KAAL,CAAWkE,gBAAZ,IAAgC,KAAK3D,aAAzC,EAAwD;UAC7Df,MAAM,CAAC0D,mBAAP,CAA2B,QAA3B,EAAqC,KAAK3C,aAA1C;UACA,KAAKA,aAAL,GAAqB,IAArB;QACD;MACF;IAnJgD;MAAAY,GAAA;MAAAC,KAAA,EAqJjD,SAAAN,OAAOa,EAAP,EAAW;QACT,KAAKA,EAAL,GAAUA,EAAV;QAEA,IAAI,KAAK3B,KAAL,CAAWsE,KAAX,IAAoB/E,SAAxB,EAAmC;UACjCC,MAAM,CAAC+E,EAAP,GAAY,KAAK5C,EAAjB;QACD;MACF,CA3JgD,CA6JjD;IA7JiD;MAAAR,GAAA;MAAAC,KAAA,EA8JjD,SAAAR,kBAAA,EAAoB;QAAA,IAAA4D,MAAA;QAClBnF,UAAU,CAACkE,OAAX,CAAmB,UAACkB,SAAD,EAAe;UAChC,IAAMC,IAAI,GAAGF,MAAI,CAACxE,KAAL,CAAW,OAAOyE,SAAlB,CAAb;UACA,IAAME,OAAO,GAAGH,MAAI,CAAChE,QAAL,CAAciE,SAAd,CAAhB;UACA,IAAMG,UAAU,GAAGC,OAAO,CAACF,OAAD,CAA1B;UAEA,IAAID,IAAI,IAAI,CAACE,UAAb,EAAyB;YACvBJ,MAAI,CAACM,eAAL,CAAqBL,SAArB,EAAgCC,IAAhC;UACD,CAFD,MAEO,IAAI,CAACA,IAAD,IAASE,UAAb,EAAyB;YAC9B;YACAJ,MAAI,CAACO,kBAAL,CAAwBN,SAAxB;UACD,CAHM,MAGA,IAAIC,IAAI,IAAIE,UAAR,IAAsBF,IAAI,KAAKC,OAAnC,EAA4C;YACjD;YACAH,MAAI,CAACO,kBAAL,CAAwBN,SAAxB;YACAD,MAAI,CAACM,eAAL,CAAqBL,SAArB,EAAgCC,IAAhC;UACD;QACF,CAfD;MAgBD;IA/KgD;MAAAvD,GAAA;MAAAC,KAAA,EAiLjD,SAAA0D,gBAAgBL,SAAhB,EAA2BC,IAA3B,EAAiC;QAC/B,KAAKlE,QAAL,CAAciE,SAAd,IAA2BC,IAA3B;QACA,KAAK/C,EAAL,CAAQ8B,EAAR,CAAW,KAAKuB,kBAAL,CAAwBP,SAAxB,CAAX,EAA+C,KAAKjE,QAAL,CAAciE,SAAd,CAA/C;MACD;IApLgD;MAAAtD,GAAA;MAAAC,KAAA,EAsLjD,SAAA2D,mBAAmBN,SAAnB,EAA8B;QAC5B,KAAK9C,EAAL,CAAQ2B,cAAR,CAAuB,KAAK0B,kBAAL,CAAwBP,SAAxB,CAAvB,EAA2D,KAAKjE,QAAL,CAAciE,SAAd,CAA3D;QACA,OAAO,KAAKjE,QAAL,CAAciE,SAAd,CAAP;MACD;IAzLgD;MAAAtD,GAAA;MAAAC,KAAA,EA2LjD,SAAA4D,mBAAmBP,SAAnB,EAA8B;QAC5B,OAAO,YAAYA,SAAS,CAACQ,WAAV,EAAnB;MACD;IA7LgD;MAAA9D,GAAA;MAAAC,KAAA,EA+LjD,SAAA8D,OAAA,EAAS;QACP,oBACElG,MAAA,YAAAmG,aAAA;UACEC,EAAE,EAAE,KAAKpF,KAAL,CAAWqF,KADjB;UAEEC,KAAK,EAAE,KAAKtF,KAAL,CAAWsF,KAFpB;UAGEC,GAAG,EAAE,KAAKzE,MAHZ;UAIE0E,SAAS,EAAE,KAAKxF,KAAL,CAAWwF;QAJxB,EADF;MAQD;IAxMgD;IAAA,OAAA7F,eAAA;EAAA,EACrBX,MAAA,CAAAyG,SADqB;EA2MnD9F,eAAe,CAAC+F,SAAhB,GAA4B;IAC1B5D,IAAI,EAAE3C,UAAA,YAAUwG,OAAV,CAAkBxG,UAAA,YAAUyG,MAA5B,CADoB;IAE1B5D,MAAM,EAAE7C,UAAA,YAAUyG,MAFQ;IAG1B7D,MAAM,EAAE5C,UAAA,YAAUyG,MAHQ;IAI1B3D,MAAM,EAAE9C,UAAA,YAAUwG,OAAV,CAAkBxG,UAAA,YAAUyG,MAA5B,CAJkB;IAK1B/C,QAAQ,EAAE1D,UAAA,YAAU0G,MALM;IAM1BxD,aAAa,EAAElD,UAAA,YAAU2G,IANC;IAO1B7C,OAAO,EAAE9D,UAAA,YAAU2G,IAPO;IAQ1B3D,OAAO,EAAEhD,UAAA,YAAU2G,IARO;IAS1B/C,QAAQ,EAAE5D,UAAA,YAAU2G,IATM;IAU1BxB,KAAK,EAAEnF,UAAA,YAAU4G,IAVS;IAW1BT,KAAK,EAAEnG,UAAA,YAAUyG,MAXS;IAY1BJ,SAAS,EAAErG,UAAA,YAAU6G,MAZK;IAa1B9B,gBAAgB,EAAE/E,UAAA,YAAU4G,IAbF;IAc1BV,KAAK,EAAElG,UAAA,YAAU6G;EAdS,CAA5B;EAiBA3G,UAAU,CAACkE,OAAX,CAAmB,UAACkB,SAAD,EAAe;IAChC9E,eAAe,CAAC+F,SAAhB,CAA0B,OAAOjB,SAAjC,IAA8CtF,UAAA,YAAU2G,IAAxD;EACD,CAFD;EAIAnG,eAAe,CAACsG,YAAhB,GAA+B;IAC7B3B,KAAK,EAAE,KADsB;IAE7BJ,gBAAgB,EAAE,KAFW;IAG7BpC,IAAI,EAAE,EAHuB;IAI7BwD,KAAK,EAAE;MAACY,QAAQ,EAAE,UAAX;MAAuBC,OAAO,EAAE;IAAhC;EAJsB,CAA/B;EAOA,OAAOxG,eAAP;AACD", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}