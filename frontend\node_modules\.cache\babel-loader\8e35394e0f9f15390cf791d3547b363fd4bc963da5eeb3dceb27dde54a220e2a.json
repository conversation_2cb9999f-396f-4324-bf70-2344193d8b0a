{"ast": null, "code": "\"use strict\";\n\nObject.defineProperty(exports, \"__esModule\", {\n  value: true\n});\nexports[\"default\"] = void 0;\nvar _factory = _interopRequireDefault(require(\"./factory\"));\nvar _plotly = _interopRequireDefault(require(\"plotly.js/dist/plotly\"));\nfunction _interopRequireDefault(obj) {\n  return obj && obj.__esModule ? obj : {\n    \"default\": obj\n  };\n}\nvar PlotComponent = (0, _factory[\"default\"])(_plotly[\"default\"]);\nvar _default = PlotComponent;\nexports[\"default\"] = _default;", "map": {"version": 3, "names": ["_factory", "_interopRequireDefault", "require", "_plotly", "PlotComponent"], "sources": ["C:\\Users\\<USER>\\Pumptimemodel\\frontend\\node_modules\\src\\react-plotly.js"], "sourcesContent": ["import plotComponentFactory from './factory';\nimport Plotly from 'plotly.js/dist/plotly';\n\nconst PlotComponent = plotComponentFactory(Plotly);\n\nexport default PlotComponent;\n"], "mappings": ";;;;;;AAAA,IAAAA,QAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,OAAA,GAAAF,sBAAA,CAAAC,OAAA;;;;;;AAEA,IAAME,aAAa,GAAG,IAAAJ,QAAA,aAAqBG,OAAA,WAArB,CAAtB;eAEeC,a", "ignoreList": []}, "metadata": {}, "sourceType": "script", "externalDependencies": []}