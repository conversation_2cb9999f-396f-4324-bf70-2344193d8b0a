{"ast": null, "code": "export { unstable_detectScrollType as detectScrollType, unstable_getNormalizedScrollLeft as getNormalizedScrollLeft } from '@mui/utils';", "map": {"version": 3, "names": ["unstable_detectScrollType", "detectScrollType", "unstable_getNormalizedScrollLeft", "getNormalizedScrollLeft"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/material/utils/scrollLeft.js"], "sourcesContent": ["export { unstable_detectScrollType as detectScrollType, unstable_getNormalizedScrollLeft as getNormalizedScrollLeft } from '@mui/utils';"], "mappings": "AAAA,SAASA,yBAAyB,IAAIC,gBAAgB,EAAEC,gCAAgC,IAAIC,uBAAuB,QAAQ,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}