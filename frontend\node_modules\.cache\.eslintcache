[{"C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\App.js": "2", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\DataValidation.js": "3", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\DataProcessing.js": "4", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\DataUpload.js": "5", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\ModelEvaluation.js": "6", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\FeatureManagement.js": "7", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\ModelTraining.js": "8", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\PredictionInterface.js": "9", "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\services\\apiService.js": "10"}, {"size": 254, "mtime": 1749119125544, "results": "11", "hashOfConfig": "12"}, {"size": 8496, "mtime": 1749561867151, "results": "13", "hashOfConfig": "12"}, {"size": 7412, "mtime": 1749561498445, "results": "14", "hashOfConfig": "12"}, {"size": 11484, "mtime": 1749561543213, "results": "15", "hashOfConfig": "12"}, {"size": 9412, "mtime": 1749119249589, "results": "16", "hashOfConfig": "12"}, {"size": 23030, "mtime": 1749720795798, "results": "17", "hashOfConfig": "12"}, {"size": 21567, "mtime": 1749716904099, "results": "18", "hashOfConfig": "12"}, {"size": 15212, "mtime": 1749561678136, "results": "19", "hashOfConfig": "12"}, {"size": 19724, "mtime": 1749561823147, "results": "20", "hashOfConfig": "12"}, {"size": 5017, "mtime": 1749119206172, "results": "21", "hashOfConfig": "12"}, {"filePath": "22", "messages": "23", "suppressedMessages": "24", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "36lcig", {"filePath": "25", "messages": "26", "suppressedMessages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "28", "messages": "29", "suppressedMessages": "30", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "31", "messages": "32", "suppressedMessages": "33", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "34", "messages": "35", "suppressedMessages": "36", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\App.js", ["52"], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\DataValidation.js", [], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\DataProcessing.js", ["53"], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\DataUpload.js", [], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\ModelEvaluation.js", ["54", "55", "56"], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\FeatureManagement.js", [], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\ModelTraining.js", ["57", "58", "59", "60", "61"], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\components\\PredictionInterface.js", ["62", "63", "64", "65"], [], "C:\\Users\\<USER>\\Pumptimemodel\\frontend\\src\\services\\apiService.js", [], [], {"ruleId": "66", "severity": 1, "message": "67", "line": 24, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 24, "endColumn": 7}, {"ruleId": "66", "severity": 1, "message": "70", "line": 22, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 22, "endColumn": 14}, {"ruleId": "66", "severity": 1, "message": "70", "line": 30, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 30, "endColumn": 14}, {"ruleId": "66", "severity": 1, "message": "71", "line": 31, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 31, "endColumn": 8}, {"ruleId": "72", "severity": 1, "message": "73", "line": 46, "column": 6, "nodeType": "74", "endLine": 46, "endColumn": 17, "suggestions": "75"}, {"ruleId": "66", "severity": 1, "message": "76", "line": 15, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 15, "endColumn": 10}, {"ruleId": "66", "severity": 1, "message": "70", "line": 29, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 29, "endColumn": 14}, {"ruleId": "66", "severity": 1, "message": "71", "line": 30, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 30, "endColumn": 8}, {"ruleId": "66", "severity": 1, "message": "77", "line": 52, "column": 10, "nodeType": "68", "messageId": "69", "endLine": 52, "endColumn": 25}, {"ruleId": "66", "severity": 1, "message": "78", "line": 251, "column": 45, "nodeType": "68", "messageId": "69", "endLine": 251, "endColumn": 61}, {"ruleId": "66", "severity": 1, "message": "79", "line": 17, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 17, "endColumn": 7}, {"ruleId": "66", "severity": 1, "message": "80", "line": 32, "column": 3, "nodeType": "68", "messageId": "69", "endLine": 32, "endColumn": 7}, {"ruleId": "66", "severity": 1, "message": "81", "line": 59, "column": 26, "nodeType": "68", "messageId": "69", "endLine": 59, "endColumn": 43}, {"ruleId": "72", "severity": 1, "message": "82", "line": 66, "column": 6, "nodeType": "74", "endLine": 66, "endColumn": 8, "suggestions": "83"}, "no-unused-vars", "'Info' is defined but never used.", "Identifier", "unusedVar", "'CheckCircle' is defined but never used.", "'Error' is defined but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'fetchFeatureImportance' and 'fetchModelPerformance'. Either include them or remove the dependency array.", "ArrayExpression", ["84"], "'Divider' is defined but never used.", "'trainingSuccess' is assigned a value but never used.", "'model_comparison' is assigned a value but never used.", "'Chip' is defined but never used.", "'Help' is defined but never used.", "'setProductOptions' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'predictionInput'. Either include it or remove the dependency array. You can also replace multiple useState variables with useReducer if 'setBatchInputs' needs the current value of 'predictionInput'.", ["85"], {"desc": "86", "fix": "87"}, {"desc": "88", "fix": "89"}, "Update the dependencies array to be: [bestModel, fetchFeatureImportance, fetchModelPerformance]", {"range": "90", "text": "91"}, "Update the dependencies array to be: [predictionInput]", {"range": "92", "text": "93"}, [997, 1008], "[bestModel, fetchFeatureImportance, fetchModelPerformance]", [1511, 1513], "[predictionInput]"]