{"ast": null, "code": "import style from './style';\nconst boxShadow = style({\n  prop: 'boxShadow',\n  themeKey: 'shadows'\n});\nexport default boxShadow;", "map": {"version": 3, "names": ["style", "boxShadow", "prop", "<PERSON><PERSON><PERSON>"], "sources": ["C:/Users/<USER>/Pumptimemodel/frontend/node_modules/@mui/system/esm/shadows.js"], "sourcesContent": ["import style from './style';\nconst boxShadow = style({\n  prop: 'boxShadow',\n  themeKey: 'shadows'\n});\nexport default boxShadow;"], "mappings": "AAAA,OAAOA,KAAK,MAAM,SAAS;AAC3B,MAAMC,SAAS,GAAGD,KAAK,CAAC;EACtBE,IAAI,EAAE,WAAW;EACjBC,QAAQ,EAAE;AACZ,CAAC,CAAC;AACF,eAAeF,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}